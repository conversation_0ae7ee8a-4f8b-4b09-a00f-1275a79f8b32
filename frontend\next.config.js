/** @type {import('next').NextConfig} */
const nextConfig = {
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:8000/api/:path*',
      },
    ];
  },
  // Fix chunk loading errors
  experimental: {
    esmExternals: false
  },
  // Disable webpack cache to prevent chunk errors
  webpack: (config, { dev }) => {
    if (dev) {
      config.cache = false;
    }
    return config;
  },
  // Optimize for stability
  swcMinify: false
};

module.exports = nextConfig;
