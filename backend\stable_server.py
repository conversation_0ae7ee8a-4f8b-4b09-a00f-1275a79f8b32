#!/usr/bin/env python3
"""
Stable FastAPI server for Niveshtor - Minimal and Reliable
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import uvicorn
import jwt
import hashlib
import logging
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title="Niveshtor Stable API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
SECRET_KEY = "your-secret-key-here-change-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Security
security = HTTPBearer()

# Pydantic Models
class UserLogin(BaseModel):
    email: EmailStr
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

# Simple in-memory user store
DEMO_USER = {
    "id": 1,
    "name": "Demo User",
    "email": "<EMAIL>",
    "password_hash": hashlib.sha256("demo123".encode()).hexdigest()
}

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: int = payload.get("sub")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid authentication credentials")
        return {"id": user_id, "email": payload.get("email")}
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid authentication credentials")

@app.get("/")
def read_root():
    return {"message": "Niveshtor Stable API is running", "status": "healthy"}

@app.post("/api/auth/login", response_model=Token)
def login(user_credentials: UserLogin):
    """Simple login endpoint"""
    try:
        print(f"🔐 Login attempt for: {user_credentials.email}")
        
        # Check credentials
        if (user_credentials.email == DEMO_USER["email"] and 
            hashlib.sha256(user_credentials.password.encode()).hexdigest() == DEMO_USER["password_hash"]):
            
            access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = create_access_token(
                data={"sub": DEMO_USER["id"], "email": DEMO_USER["email"]}, 
                expires_delta=access_token_expires
            )
            
            print(f"✅ Login successful for: {user_credentials.email}")
            return {"access_token": access_token, "token_type": "bearer"}
        else:
            print(f"❌ Invalid credentials for: {user_credentials.email}")
            raise HTTPException(status_code=401, detail="Incorrect email or password")
            
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        raise HTTPException(status_code=500, detail="Login failed")

@app.get("/api/boh/eligible")
def get_boh_eligible_stocks(current_user: dict = Depends(get_current_user)):
    """Simple BOH eligible stocks endpoint with mock data"""
    try:
        print(f"🔍 BOH Filter API called by user {current_user['id']}")
        
        # Simple mock data for testing
        mock_stocks = [
            {
                "stock_name": "RELIANCE",
                "cmp": 2450.50,
                "week_52_low": 2100.00,
                "week_52_low_date": "2024-03-15",
                "week_52_high": 2800.00,
                "week_52_high_date": "2024-01-10",
                "boh_eligible": "Yes",
                "is_eligible": True
            },
            {
                "stock_name": "TCS",
                "cmp": 3650.75,
                "week_52_low": 3200.00,
                "week_52_low_date": "2024-02-20",
                "week_52_high": 4100.00,
                "week_52_high_date": "2024-01-05",
                "boh_eligible": "Yes",
                "is_eligible": True
            },
            {
                "stock_name": "INFY",
                "cmp": 1580.25,
                "week_52_low": 1350.00,
                "week_52_low_date": "2024-04-10",
                "week_52_high": 1800.00,
                "week_52_high_date": "2024-02-01",
                "boh_eligible": "Yes",
                "is_eligible": True
            }
        ]
        
        print(f"📊 Returning {len(mock_stocks)} BOH eligible stocks")
        
        return {
            "stocks": mock_stocks,
            "total_count": len(mock_stocks),
            "eligible_count": len([s for s in mock_stocks if s["is_eligible"]]),
            "connection_status": {
                "connected": True,
                "yahoo_finance": True,
                "yahoo_finance_message": "Mock data - stable mode",
                "angel_one_authenticated": False,
                "broker_count": 0,
                "user_id": None,
                "status": "Stable Mode: Mock Data for Testing",
                "data_source": "mock_stable",
                "architecture": "simplified_stable",
                "market_status": "Mock data for stability testing"
            }
        }
        
    except Exception as e:
        print(f"❌ Error in BOH filter: {str(e)}")
        return {
            "stocks": [],
            "total_count": 0,
            "eligible_count": 0,
            "connection_status": {
                "connected": False,
                "status": f"Error: {str(e)}"
            }
        }

if __name__ == "__main__":
    print("🚀 Starting Niveshtor Stable Backend Server...")
    print("📡 Server: http://localhost:8000")
    print("📚 API Docs: http://localhost:8000/docs")
    print("🔧 Stable Mode: Simplified endpoints for reliability")
    print("=" * 50)
    
    uvicorn.run(
        "stable_server:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
