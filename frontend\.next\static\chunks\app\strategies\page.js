/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/strategies/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/build/polyfills/process.js ***!
  \***********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nvar _global_process, _global_process1;\nmodule.exports = ((_global_process = __webpack_require__.g.process) == null ? void 0 : _global_process.env) && typeof ((_global_process1 = __webpack_require__.g.process) == null ? void 0 : _global_process1.env) === \"object\" ? __webpack_require__.g.process : __webpack_require__(/*! next/dist/compiled/process */ \"(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js\");\n\n//# sourceMappingURL=process.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvcG9seWZpbGxzL3Byb2Nlc3MuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLHFDQUFxQyxxQkFBTSxpRkFBaUYscUJBQU0sa0VBQWtFLHFCQUFNLFdBQVcsbUJBQU8sQ0FBQyw0R0FBNEI7O0FBRXpQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvcG9seWZpbGxzL3Byb2Nlc3MuanM/YThjNiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfZ2xvYmFsX3Byb2Nlc3MsIF9nbG9iYWxfcHJvY2VzczE7XG5tb2R1bGUuZXhwb3J0cyA9ICgoX2dsb2JhbF9wcm9jZXNzID0gZ2xvYmFsLnByb2Nlc3MpID09IG51bGwgPyB2b2lkIDAgOiBfZ2xvYmFsX3Byb2Nlc3MuZW52KSAmJiB0eXBlb2YgKChfZ2xvYmFsX3Byb2Nlc3MxID0gZ2xvYmFsLnByb2Nlc3MpID09IG51bGwgPyB2b2lkIDAgOiBfZ2xvYmFsX3Byb2Nlc3MxLmVudikgPT09IFwib2JqZWN0XCIgPyBnbG9iYWwucHJvY2VzcyA6IHJlcXVpcmUoXCJuZXh0L2Rpc3QvY29tcGlsZWQvcHJvY2Vzc1wiKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cHJvY2Vzcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Capp%5Cstrategies%5Cpage.tsx&server=false!":
/*!************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Capp%5Cstrategies%5Cpage.tsx&server=false! ***!
  \************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/strategies/page.tsx */ \"(app-pages-browser)/./src/app/strategies/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNtYWhlc2glNUNEb2N1bWVudHMlNUNOaXZlc2h0b3IlNUNmcm9udGVuZCU1Q3NyYyU1Q2FwcCU1Q3N0cmF0ZWdpZXMlNUNwYWdlLnRzeCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/YmU4OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG1haGVzaFxcXFxEb2N1bWVudHNcXFxcTml2ZXNodG9yXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcc3RyYXRlZ2llc1xcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Capp%5Cstrategies%5Cpage.tsx&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/strategies/page.tsx":
/*!*************************************!*\
  !*** ./src/app/strategies/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst StrategiesPage = ()=>{\n    _s();\n    const { isAuthenticated, user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const [activeStrategy, setActiveStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"weekly-high\");\n    // Weekly High Strategy state\n    const [weeklyHighSignals, setWeeklyHighSignals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [bohStocks, setBohStocks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expandedRows, setExpandedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [processingStatus, setProcessingStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const strategies = [\n        {\n            id: \"weekly-high\",\n            name: \"Weekly High\",\n            description: \"Breakout strategy based on weekly high levels\",\n            status: \"Active\",\n            performance: \"+12.5%\",\n            trades: 45,\n            winRate: \"68%\"\n        },\n        {\n            id: \"rsi\",\n            name: \"RSI Mean Reversion\",\n            description: \"Mean reversion strategy using RSI indicator\",\n            status: \"Active\",\n            performance: \"+8.3%\",\n            trades: 32,\n            winRate: \"72%\"\n        },\n        {\n            id: \"consolidated-stock\",\n            name: \"Consolidated Stock\",\n            description: \"Breakout from consolidation patterns\",\n            status: \"Paused\",\n            performance: \"+5.1%\",\n            trades: 18,\n            winRate: \"61%\"\n        }\n    ];\n    const tabs = [\n        {\n            id: \"signal\",\n            name: \"Signal\",\n            emoji: \"\\uD83D\\uDCCA\"\n        },\n        {\n            id: \"gtt-order\",\n            name: \"GTT Order\",\n            emoji: \"⚡\"\n        },\n        {\n            id: \"current-holdings\",\n            name: \"Current Holdings\",\n            emoji: \"\\uD83D\\uDCBC\"\n        }\n    ];\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"signal\");\n    // Handle URL parameters for strategy selection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const strategy = searchParams.get(\"strategy\");\n        if (strategy && [\n            \"weekly-high\",\n            \"rsi\",\n            \"consolidated-stock\"\n        ].includes(strategy)) {\n            setActiveStrategy(strategy);\n        }\n    }, [\n        searchParams\n    ]);\n    // Weekly High Strategy Functions\n    const fetchWeeklyHighSignals = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Always try to get a fresh token to avoid 401 errors\n            console.log(\"\\uD83D\\uDD10 Getting fresh authentication token for Weekly High Strategy...\");\n            console.log(\"\\uD83C\\uDF10 Backend server URL: http://localhost:8000\");\n            setProcessingStatus(\"Authenticating with demo credentials...\");\n            const loginResponse = await fetch(\"http://localhost:8000/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email: \"<EMAIL>\",\n                    password: \"demo123\"\n                })\n            });\n            console.log(\"\\uD83D\\uDCE1 Login response status:\", loginResponse.status, loginResponse.statusText);\n            if (!loginResponse.ok) {\n                const errorText = await loginResponse.text();\n                console.error(\"❌ Weekly High Strategy login failed:\", loginResponse.status, errorText);\n                throw new Error(\"Authentication failed: \".concat(loginResponse.status, \" \").concat(loginResponse.statusText, \". Error: \").concat(errorText));\n            }\n            const loginData = await loginResponse.json();\n            const token = loginData.access_token;\n            localStorage.setItem(\"auth_token\", token);\n            console.log(\"✅ Fresh token obtained for Weekly High Strategy\");\n            // Step 1: Fetch BOH eligible stocks from backend API with extended timeout\n            console.log(\"\\uD83D\\uDD0D Fetching BOH eligible stocks from backend...\");\n            setProcessingStatus(\"Fetching BOH eligible stocks from backend...\");\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>{\n                console.log(\"⏰ BOH API request timed out after 60 seconds\");\n                controller.abort();\n            }, 60000); // Extended to 60 second timeout for BOH API\n            let eligibleStocks = [];\n            try {\n                var _bohData_stocks;\n                console.log(\"\\uD83C\\uDF10 Making BOH API request with token:\", token ? token.substring(0, 20) + \"...\" : \"No token\");\n                const bohResponse = await fetch(\"http://localhost:8000/api/boh/eligible\", {\n                    method: \"GET\",\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(token),\n                        \"Content-Type\": \"application/json\"\n                    },\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                console.log(\"\\uD83D\\uDCE1 BOH API response status:\", bohResponse.status, bohResponse.statusText);\n                if (!bohResponse.ok) {\n                    const errorText = await bohResponse.text();\n                    console.error(\"❌ BOH API error response:\", errorText);\n                    throw new Error(\"BOH API returned \".concat(bohResponse.status, \": \").concat(bohResponse.statusText));\n                }\n                const bohData = await bohResponse.json();\n                console.log(\"\\uD83D\\uDCCA BOH API data received:\", {\n                    totalStocks: ((_bohData_stocks = bohData.stocks) === null || _bohData_stocks === void 0 ? void 0 : _bohData_stocks.length) || 0,\n                    eligibleCount: bohData.eligible_count || 0\n                });\n                // Filter only BOH eligible stocks (boh_eligible = \"Yes\")\n                eligibleStocks = bohData.stocks.filter((stock)=>stock.is_eligible && stock.boh_eligible === \"Yes\");\n                setBohStocks(eligibleStocks);\n                console.log(\"✅ Found \".concat(eligibleStocks.length, \" BOH eligible stocks from backend\"));\n                if (eligibleStocks.length === 0) {\n                    setWeeklyHighSignals([]);\n                    setLastUpdated(new Date());\n                    setProcessingStatus(\"\");\n                    console.log(\"⚠️ No BOH eligible stocks found\");\n                    return;\n                }\n            } catch (error) {\n                var _error_message;\n                clearTimeout(timeoutId);\n                console.error(\"❌ Failed to fetch BOH eligible stocks:\", error);\n                // Provide more specific error messages\n                if (error.name === \"AbortError\") {\n                    throw new Error(\"BOH API request timed out. The backend may be processing a large number of stocks. Please try again.\");\n                } else if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"Failed to fetch\")) {\n                    throw new Error(\"Unable to connect to the backend server. Please check if the server is running.\");\n                } else {\n                    throw new Error(\"Failed to fetch BOH eligible stocks: \".concat(error.message));\n                }\n            }\n            // Step 2: Calculate Weekly High signals with real Yahoo Finance data\n            console.log(\"\\uD83D\\uDCC8 Calculating Weekly High signals with Yahoo Finance data...\");\n            setProcessingStatus(\"Processing \".concat(eligibleStocks.length, \" BOH eligible stocks for Weekly High signals...\"));\n            const signals = await calculateWeeklyHighSignals(eligibleStocks, []);\n            setWeeklyHighSignals(signals);\n            setLastUpdated(new Date());\n            setProcessingStatus(\"\");\n            console.log(\"\\uD83C\\uDFAF Generated \".concat(signals.length, \" Weekly High signals from \").concat(eligibleStocks.length, \" BOH eligible stocks\"));\n        } catch (error) {\n            console.error(\"Error in fetchWeeklyHighSignals:\", error);\n            setError(error instanceof Error ? error.message : \"Unknown error occurred\");\n            setProcessingStatus(\"\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const calculateWeeklyHighSignals = async (eligibleStocks, holdings)=>{\n        const signals = [];\n        const holdingSymbols = new Set(holdings.map((h)=>h.symbol));\n        let successCount = 0;\n        let failureCount = 0;\n        console.log(\"\\uD83D\\uDCCA Processing \".concat(eligibleStocks.length, \" BOH eligible stocks for Weekly High signals...\"));\n        for(let i = 0; i < eligibleStocks.length; i++){\n            const stock = eligibleStocks[i];\n            try {\n                console.log(\"\\uD83D\\uDD0D Processing \".concat(i + 1, \"/\").concat(eligibleStocks.length, \": \").concat(stock.stock_name, \"...\"));\n                setProcessingStatus(\"Processing \".concat(i + 1, \"/\").concat(eligibleStocks.length, \": \").concat(stock.stock_name, \" (✅\").concat(successCount, \" ❌\").concat(failureCount, \")\"));\n                // Check if already purchased\n                const alreadyPurchased = holdingSymbols.has(stock.stock_name);\n                // Try multiple approaches to get OHLC data\n                let weeklyOHLCData = [];\n                let lastWeekHighestPrice = 0;\n                let dataSource = \"yahoo_finance\";\n                try {\n                    // First attempt: Fetch real OHLC data from Yahoo Finance\n                    weeklyOHLCData = await fetchYahooFinanceOHLC(stock.stock_name);\n                    lastWeekHighestPrice = getLastWeekHighestPrice(weeklyOHLCData);\n                    dataSource = \"yahoo_finance\";\n                } catch (yahooError) {\n                    console.log(\"⚠️ Yahoo Finance failed for \".concat(stock.stock_name, \", marking as data unavailable\"));\n                    // No fallback calculation - mark as unavailable\n                    dataSource = \"unavailable\";\n                    lastWeekHighestPrice = 0;\n                    weeklyOHLCData = [];\n                }\n                // Calculate suggested buy price and other metrics\n                const suggestedBuyPrice = dataSource === \"yahoo_finance\" ? lastWeekHighestPrice + 0.05 : 0;\n                const percentageDifference = dataSource === \"yahoo_finance\" ? (stock.cmp - suggestedBuyPrice) / suggestedBuyPrice * 100 : 0;\n                const suggestedGTTQuantity = dataSource === \"yahoo_finance\" ? Math.floor(2000 / suggestedBuyPrice) : 0;\n                const signal = {\n                    stock_name: stock.stock_name,\n                    cmp: stock.cmp,\n                    already_purchased: alreadyPurchased,\n                    last_week_highest_price: lastWeekHighestPrice,\n                    suggested_buy_price: suggestedBuyPrice,\n                    percentage_difference: percentageDifference,\n                    suggested_gtt_quantity: suggestedGTTQuantity,\n                    weekly_ohlc_data: weeklyOHLCData,\n                    data_source: dataSource\n                };\n                signals.push(signal);\n                if (dataSource === \"yahoo_finance\") {\n                    successCount++;\n                    console.log(\"✅ \".concat(stock.stock_name, \": Weekly High = ₹\").concat(lastWeekHighestPrice.toFixed(2), \", Buy Price = ₹\").concat(suggestedBuyPrice.toFixed(2), \", Diff = \").concat(percentageDifference.toFixed(2), \"%\"));\n                } else {\n                    failureCount++;\n                    console.log(\"❌ \".concat(stock.stock_name, \": Data unavailable\"));\n                }\n                // Add delay to avoid overwhelming the API\n                if (i < eligibleStocks.length - 1) {\n                    await new Promise((resolve)=>setTimeout(resolve, 100));\n                }\n            } catch (error) {\n                failureCount++;\n                console.error(\"❌ Error processing \".concat(stock.stock_name, \":\"), error);\n                // Add signal with unavailable data\n                const signal = {\n                    stock_name: stock.stock_name,\n                    cmp: stock.cmp,\n                    already_purchased: holdingSymbols.has(stock.stock_name),\n                    last_week_highest_price: 0,\n                    suggested_buy_price: 0,\n                    percentage_difference: 0,\n                    suggested_gtt_quantity: 0,\n                    weekly_ohlc_data: [],\n                    data_source: \"unavailable\"\n                };\n                signals.push(signal);\n            }\n        }\n        console.log(\"\\uD83C\\uDFAF Successfully generated \".concat(signals.length, \" Weekly High signals from \").concat(eligibleStocks.length, \" BOH eligible stocks (✅\").concat(successCount, \" ❌\").concat(failureCount, \")\"));\n        setProcessingStatus(\"\");\n        return signals;\n    };\n    const fetchYahooFinanceOHLC = async (stockSymbol)=>{\n        // Try multiple approaches to get data\n        const attempts = [\n            // Attempt 1: Current week data\n            ()=>fetchYahooDataWithDates(stockSymbol, 0),\n            // Attempt 2: Previous week data\n            ()=>fetchYahooDataWithDates(stockSymbol, 1),\n            // Attempt 3: Two weeks ago data\n            ()=>fetchYahooDataWithDates(stockSymbol, 2)\n        ];\n        for(let i = 0; i < attempts.length; i++){\n            try {\n                console.log(\"\\uD83D\\uDCCA Attempt \".concat(i + 1, \"/3: Fetching OHLC data for \").concat(stockSymbol, \"...\"));\n                const data = await attempts[i]();\n                if (data && data.length > 0) {\n                    console.log(\"✅ Successfully fetched \".concat(data.length, \" days of OHLC data for \").concat(stockSymbol, \" (attempt \").concat(i + 1, \")\"));\n                    return data;\n                }\n            } catch (error) {\n                console.log(\"⚠️ Attempt \".concat(i + 1, \" failed for \").concat(stockSymbol, \":\"), error);\n                if (i === attempts.length - 1) {\n                    throw error;\n                }\n            }\n        }\n        throw new Error(\"Failed to fetch OHLC data for \".concat(stockSymbol, \" after \").concat(attempts.length, \" attempts\"));\n    };\n    const fetchYahooDataWithDates = async (stockSymbol, weeksBack)=>{\n        const now = new Date();\n        const startDate = new Date(now);\n        startDate.setDate(now.getDate() - 7 * (weeksBack + 1)); // Go back weeksBack+1 weeks\n        const endDate = new Date(now);\n        endDate.setDate(now.getDate() - 7 * weeksBack); // End weeksBack weeks ago\n        const formatDate = (date)=>{\n            return date.toISOString().split(\"T\")[0];\n        };\n        const startDateStr = formatDate(startDate);\n        const endDateStr = formatDate(endDate);\n        console.log(\"\\uD83D\\uDCC5 Fetching \".concat(stockSymbol, \" data from \").concat(startDateStr, \" to \").concat(endDateStr, \" (\").concat(weeksBack, \" weeks back)\"));\n        const corsProxy = \"https://cors-anywhere.herokuapp.com/\";\n        const yahooUrl = \"\".concat(corsProxy, \"https://query1.finance.yahoo.com/v7/finance/download/\").concat(stockSymbol, \".NS?period1=\").concat(Math.floor(startDate.getTime() / 1000), \"&period2=\").concat(Math.floor(endDate.getTime() / 1000), \"&interval=1d&events=history\");\n        const response = await fetch(yahooUrl, {\n            headers: {\n                \"X-Requested-With\": \"XMLHttpRequest\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"Yahoo Finance API error: \".concat(response.status, \" \").concat(response.statusText));\n        }\n        const csvText = await response.text();\n        const lines = csvText.trim().split(\"\\n\");\n        if (lines.length <= 1) {\n            throw new Error(\"No data available from Yahoo Finance\");\n        }\n        const ohlcData = [];\n        // Skip header row\n        for(let i = 1; i < lines.length; i++){\n            const columns = lines[i].split(\",\");\n            if (columns.length >= 6) {\n                const date = columns[0];\n                const open = parseFloat(columns[1]);\n                const high = parseFloat(columns[2]);\n                const low = parseFloat(columns[3]);\n                const close = parseFloat(columns[4]);\n                if (!isNaN(open) && !isNaN(high) && !isNaN(low) && !isNaN(close)) {\n                    const dayOfWeek = new Date(date).toLocaleDateString(\"en-US\", {\n                        weekday: \"long\"\n                    });\n                    ohlcData.push({\n                        date,\n                        day: dayOfWeek,\n                        open: Math.round(open * 100) / 100,\n                        high: Math.round(high * 100) / 100,\n                        low: Math.round(low * 100) / 100,\n                        close: Math.round(close * 100) / 100\n                    });\n                }\n            }\n        }\n        if (ohlcData.length === 0) {\n            throw new Error(\"No valid OHLC data found\");\n        }\n        return ohlcData;\n    };\n    const getLastWeekHighestPrice = (ohlcData)=>{\n        if (ohlcData.length === 0) return 0;\n        // Find the maximum 'High' value from all days\n        const weeklyHigh = Math.max(...ohlcData.map((day)=>day.high));\n        return Math.round(weeklyHigh * 100) / 100;\n    };\n    const toggleRowExpansion = (stockName)=>{\n        const newExpandedRows = new Set(expandedRows);\n        if (newExpandedRows.has(stockName)) {\n            newExpandedRows.delete(stockName);\n        } else {\n            newExpandedRows.add(stockName);\n        }\n        setExpandedRows(newExpandedRows);\n    };\n    // Load data when strategy changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeStrategy === \"weekly-high\") {\n            // Always try to fetch signals, the function handles authentication internally\n            fetchWeeklyHighSignals();\n        }\n    }, [\n        activeStrategy\n    ]);\n    const renderWeeklyHighSignalTab = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent\",\n                                        children: \"\\uD83D\\uDCC8 Weekly High Strategy - Signal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: \"Breakout opportunities from BOH eligible stocks with strong weekly price structure\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: fetchWeeklyHighSignals,\n                                disabled: loading,\n                                className: \"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"Processing...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: \"\\uD83D\\uDD04 Refresh Signals\"\n                                }, void 0, false)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 11\n                    }, undefined),\n                    processingStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-800 text-sm font-medium\",\n                                    children: processingStatus\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                            lineNumber: 486,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 485,\n                        columnNumber: 13\n                    }, undefined),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-600 text-2xl mr-3\",\n                                            children: \"❌\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-red-800 font-medium\",\n                                                    children: \"Error\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-600 text-sm\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setError(null);\n                                        fetchWeeklyHighSignals();\n                                    },\n                                    className: \"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n                                    children: \"Retry\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 13\n                    }, undefined),\n                    lastUpdated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: \"Last Updated:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 15\n                            }, undefined),\n                            \" \",\n                            lastUpdated.toLocaleString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 13\n                    }, undefined),\n                    weeklyHighSignals.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"CMP\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Weekly High\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Data Source\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Buy Price\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"% Diff\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"GTT Qty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"OHLC Data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white divide-y divide-gray-200\",\n                                    children: weeklyHighSignals.map((signal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: index % 2 === 0 ? \"bg-white\" : \"bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: signal.stock_name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    signal.cmp.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: signal.data_source === \"unavailable\" ? \"Data Not Available\" : \"₹\".concat(signal.last_week_highest_price.toFixed(2))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(signal.data_source === \"yahoo_finance\" ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"),\n                                                                children: signal.data_source === \"yahoo_finance\" ? \"\\uD83D\\uDCCA Real Data\" : \"❌ Unavailable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-blue-600\",\n                                                                children: signal.data_source === \"unavailable\" ? \"N/A\" : \"₹\".concat(signal.suggested_buy_price.toFixed(2))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium \".concat(signal.data_source === \"unavailable\" ? \"text-gray-500\" : signal.percentage_difference >= 0 ? \"text-green-600\" : \"text-red-600\"),\n                                                                children: signal.data_source === \"unavailable\" ? \"N/A\" : \"\".concat(signal.percentage_difference >= 0 ? \"+\" : \"\").concat(signal.percentage_difference.toFixed(2), \"%\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: signal.data_source === \"unavailable\" ? \"N/A\" : signal.suggested_gtt_quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>toggleRowExpansion(signal.stock_name),\n                                                                className: \"inline-flex items-center px-3 py-1 border border-gray-300 text-xs leading-4 font-medium rounded text-gray-700 bg-white hover:bg-gray-50\",\n                                                                children: expandedRows.has(signal.stock_name) ? \"\\uD83D\\uDCCA Hide OHLC\" : \"\\uD83D\\uDCCA Show OHLC\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                expandedRows.has(signal.stock_name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 8,\n                                                        className: \"px-6 py-4 bg-gray-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: [\n                                                                        \"\\uD83D\\uDCC8 Weekly OHLC Data for \",\n                                                                        signal.stock_name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                signal.data_source === \"unavailable\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center py-8\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-red-500 text-lg mb-2\",\n                                                                            children: \"❌\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                            lineNumber: 603,\n                                                                            columnNumber: 35\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Yahoo Finance data could not be retrieved for this stock\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 35\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                                            children: \"No OHLC data available for signal calculation\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                            lineNumber: 605,\n                                                                            columnNumber: 35\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 33\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"overflow-x-auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                        className: \"min-w-full divide-y divide-gray-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                                className: \"bg-gray-100\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                            className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\",\n                                                                                            children: \"Day\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                            lineNumber: 612,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                            className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\",\n                                                                                            children: \"Date\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                            lineNumber: 613,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                            className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\",\n                                                                                            children: \"Open\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                            lineNumber: 614,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                            className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\",\n                                                                                            children: \"High\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                            lineNumber: 615,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                            className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\",\n                                                                                            children: \"Low\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                            lineNumber: 616,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                            className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\",\n                                                                                            children: \"Close\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                            lineNumber: 617,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                            className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\",\n                                                                                            children: \"Weekly High\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                            lineNumber: 618,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                    lineNumber: 611,\n                                                                                    columnNumber: 39\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                lineNumber: 610,\n                                                                                columnNumber: 37\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                                className: \"bg-white divide-y divide-gray-200\",\n                                                                                children: signal.weekly_ohlc_data.map((ohlc, ohlcIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                                                                children: ohlc.day\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                                lineNumber: 624,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                                                                children: ohlc.date\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                                lineNumber: 625,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                                                                children: [\n                                                                                                    \"₹\",\n                                                                                                    ohlc.open.toFixed(2)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                                lineNumber: 626,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                                className: \"px-4 py-2 text-sm font-medium text-green-600\",\n                                                                                                children: [\n                                                                                                    \"₹\",\n                                                                                                    ohlc.high.toFixed(2)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                                lineNumber: 627,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                                                                children: [\n                                                                                                    \"₹\",\n                                                                                                    ohlc.low.toFixed(2)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                                lineNumber: 628,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                                                                children: [\n                                                                                                    \"₹\",\n                                                                                                    ohlc.close.toFixed(2)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                                lineNumber: 629,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                                className: \"px-4 py-2 text-sm font-bold text-blue-600\",\n                                                                                                children: ohlc.high === signal.last_week_highest_price ? \"₹\".concat(signal.last_week_highest_price.toFixed(2)) : \"\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                                lineNumber: 630,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, ohlcIndex, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                        lineNumber: 623,\n                                                                                        columnNumber: 41\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                lineNumber: 621,\n                                                                                columnNumber: 37\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                        lineNumber: 609,\n                                                                        columnNumber: 35\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 33\n                                                                }, undefined),\n                                                                signal.data_source === \"yahoo_finance\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Calculation:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                    lineNumber: 641,\n                                                                                    columnNumber: 38\n                                                                                }, undefined),\n                                                                                \" Weekly High = Maximum of all 'High' values = ₹\",\n                                                                                signal.last_week_highest_price.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                            lineNumber: 641,\n                                                                            columnNumber: 35\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Suggested Buy Price:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                    lineNumber: 642,\n                                                                                    columnNumber: 38\n                                                                                }, undefined),\n                                                                                \" ₹\",\n                                                                                signal.last_week_highest_price.toFixed(2),\n                                                                                \" + ₹0.05 = ₹\",\n                                                                                signal.suggested_buy_price.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                            lineNumber: 642,\n                                                                            columnNumber: 35\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                    lineNumber: 640,\n                                                                    columnNumber: 33\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, signal.stock_name, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-6xl mb-4\",\n                                children: \"\\uD83D\\uDCCA\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 656,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No Weekly High signals available\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 657,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: loading ? \"Loading signals...\" : \"Ensure BOH eligible stocks are available and try refreshing.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 658,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                lineNumber: 455,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n            lineNumber: 453,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderTabContent = ()=>{\n        const strategy = strategies.find((s)=>s.id === activeStrategy);\n        switch(activeTab){\n            case \"signal\":\n                if (activeStrategy === \"weekly-high\") {\n                    return renderWeeklyHighSignalTab();\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: [\n                                    \"\\uD83D\\uDCC8 Current Signals - \",\n                                    strategy === null || strategy === void 0 ? void 0 : strategy.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 679,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"RELIANCE\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Signal: BUY | Price: ₹2,450\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 687,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                            children: \"Strong Buy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 690,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: \"Confidence: 85%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 684,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"TCS\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Signal: BUY | Price: ₹3,680\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                            children: \"Buy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: \"Confidence: 72%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                    lineNumber: 677,\n                    columnNumber: 11\n                }, undefined);\n            case \"gtt-order\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: [\n                                    \"⚡ GTT Orders - \",\n                                    strategy === null || strategy === void 0 ? void 0 : strategy.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 720,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"HDFC\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 727,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"GTT Buy: ₹1,580 | Target: ₹1,650\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\",\n                                                            children: \"Pending\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 731,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: \"Valid till: 30 days\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 734,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"INFY\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"GTT Sell: ₹1,420 | Stop Loss: ₹1,380\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 740,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                            children: \"Active\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 745,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: \"Valid till: 15 days\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 748,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 744,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 723,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                    lineNumber: 718,\n                    columnNumber: 11\n                }, undefined);\n            case \"current-holdings\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: [\n                                    \"\\uD83D\\uDCBC Current Holdings - \",\n                                    strategy === null || strategy === void 0 ? void 0 : strategy.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 761,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"WIPRO\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Qty: 50 | Avg Price: ₹420\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium text-green-600\",\n                                                            children: \"+₹1,250\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 772,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"+5.95%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 773,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 766,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"ICICIBANK\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Qty: 25 | Avg Price: ₹980\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 781,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium text-red-600\",\n                                                            children: \"-₹875\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 784,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"-3.57%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 785,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 783,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 764,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 760,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                    lineNumber: 759,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Select a tab\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                    lineNumber: 795,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Trading Strategies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 802,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"Manage and monitor your automated trading strategies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 803,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                lineNumber: 801,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: strategies.map((strategy)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"cursor-pointer rounded-lg border-2 p-6 transition-all \".concat(activeStrategy === strategy.id ? \"border-indigo-500 bg-indigo-50\" : \"border-gray-200 bg-white hover:border-gray-300\"),\n                            onClick: ()=>setActiveStrategy(strategy.id),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: strategy.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(strategy.status === \"Active\" ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                            children: strategy.status\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 823,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 821,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-4\",\n                                    children: strategy.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 833,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: strategy.performance\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"Performance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 837,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 835,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: strategy.trades\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 840,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"Trades\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 841,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 839,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: strategy.winRate\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 844,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"Win Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 845,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 843,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 834,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, strategy.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                            lineNumber: 812,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                    lineNumber: 810,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                lineNumber: 809,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8\",\n                        children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-indigo-500 text-indigo-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-2\",\n                                        children: tab.emoji\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 867,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tab.name\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 858,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 856,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                    lineNumber: 855,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                lineNumber: 854,\n                columnNumber: 7\n            }, undefined),\n            renderTabContent()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n        lineNumber: 800,\n        columnNumber: 5\n    }, undefined);\n};\n_s(StrategiesPage, \"JfI0OvGDiWog4gG5Wy9whsVSYXE=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams\n    ];\n});\n_c = StrategiesPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (StrategiesPage);\nvar _c;\n$RefreshReg$(_c, \"StrategiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/strategies/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; },\n/* harmony export */   withAuth: function() { return /* binding */ withAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider,withAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simplified auth check - just check localStorage without API calls\n        const checkAuth = ()=>{\n            try {\n                const token = localStorage.getItem(\"auth_token\");\n                const userInfo = localStorage.getItem(\"user_info\");\n                console.log(\"Checking authentication:\", {\n                    hasToken: !!token,\n                    hasUserInfo: !!userInfo\n                });\n                if (token && userInfo) {\n                    try {\n                        const userData = JSON.parse(userInfo);\n                        console.log(\"User data found in localStorage:\", userData);\n                        setUser(userData);\n                    } catch (error) {\n                        console.error(\"Error parsing user info:\", error);\n                        localStorage.removeItem(\"auth_token\");\n                        localStorage.removeItem(\"user_info\");\n                    }\n                } else {\n                    console.log(\"No authentication data found\");\n                }\n            } catch (error) {\n                console.error(\"Error checking authentication:\", error);\n                localStorage.removeItem(\"auth_token\");\n                localStorage.removeItem(\"user_info\");\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        // Use setTimeout to avoid blocking the initial render\n        setTimeout(checkAuth, 100);\n    }, []);\n    const login = async (email, password)=>{\n        try {\n            setIsLoading(true);\n            console.log(\"Attempting login for:\", email);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.login(email, password);\n            console.log(\"Login response:\", response);\n            if (response.success && response.data) {\n                // Check if this is demo mode (token starts with 'demo_token_')\n                const token = response.data.access_token;\n                if (token.startsWith(\"demo_token_\")) {\n                    // Demo mode - create mock user data\n                    const mockUser = {\n                        id: 1,\n                        name: \"Demo User\",\n                        email: email,\n                        phone: \"+91-9876543210\",\n                        created_at: new Date().toISOString()\n                    };\n                    setUser(mockUser);\n                    localStorage.setItem(\"user_info\", JSON.stringify(mockUser));\n                    return {\n                        success: true,\n                        message: response.message || \"Login successful (Demo Mode)\"\n                    };\n                } else {\n                    // Real backend mode - get user info from server\n                    try {\n                        const userResponse = await fetch(\"\".concat(process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\", \"/api/auth/me\"), {\n                            headers: {\n                                \"Authorization\": \"Bearer \".concat(token),\n                                \"Content-Type\": \"application/json\"\n                            }\n                        });\n                        if (userResponse.ok) {\n                            const userData = await userResponse.json();\n                            console.log(\"User data retrieved:\", userData);\n                            setUser(userData);\n                            localStorage.setItem(\"user_info\", JSON.stringify(userData));\n                            return {\n                                success: true,\n                                message: \"Login successful\"\n                            };\n                        } else {\n                            console.error(\"Failed to get user info after login\");\n                            return {\n                                success: false,\n                                message: \"Failed to get user information\"\n                            };\n                        }\n                    } catch (error) {\n                        console.error(\"Error getting user info:\", error);\n                        return {\n                            success: false,\n                            message: \"Failed to get user information\"\n                        };\n                    }\n                }\n            } else {\n                console.log(\"Login failed:\", response.message);\n                return {\n                    success: false,\n                    message: response.message || \"Login failed\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                message: \"Network error during login\"\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        try {\n            setIsLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.register(userData);\n            if (response.success) {\n                return {\n                    success: true,\n                    message: \"Registration successful. Please login.\"\n                };\n            } else {\n                return {\n                    success: false,\n                    message: response.message || \"Registration failed\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                message: \"Network error during registration\"\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = ()=>{\n        setUser(null);\n        _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.logout();\n        localStorage.removeItem(\"user_info\");\n    };\n    const value = {\n        user,\n        isAuthenticated: !!user,\n        isLoading,\n        login,\n        register,\n        logout\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AuthProvider, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\n_c = AuthProvider;\n// Higher-order component for protecting routes\nconst withAuth = (Component)=>{\n    var _s = $RefreshSig$();\n    return _s(function AuthenticatedComponent(props) {\n        _s();\n        const { isAuthenticated, isLoading } = useAuth();\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthenticated) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full space-y-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"mt-6 text-3xl font-extrabold text-gray-900\",\n                                children: \"Authentication Required\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-gray-600\",\n                                children: \"Please login to access this page\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/auth/login\",\n                                    className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\",\n                                    children: \"Go to Login\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 195,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 218,\n            columnNumber: 12\n        }, this);\n    }, \"yb/FJYAIXt7wZoU4a4YvGQ4Nlsc=\", false, function() {\n        return [\n            useAuth\n        ];\n    });\n};\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9BdXRoQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUV5RjtBQUM1QztBQWtCN0MsTUFBTU0sNEJBQWNMLG9EQUFhQSxDQUE4Qk07QUFFeEQsTUFBTUMsVUFBVTs7SUFDckIsTUFBTUMsVUFBVVAsaURBQVVBLENBQUNJO0lBQzNCLElBQUlHLFlBQVlGLFdBQVc7UUFDekIsTUFBTSxJQUFJRyxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVCxFQUFFO0dBTldEO0FBWU4sTUFBTUcsZUFBNEM7UUFBQyxFQUFFQyxRQUFRLEVBQUU7O0lBQ3BFLE1BQU0sQ0FBQ0MsTUFBTUMsUUFBUSxHQUFHWCwrQ0FBUUEsQ0FBYztJQUM5QyxNQUFNLENBQUNZLFdBQVdDLGFBQWEsR0FBR2IsK0NBQVFBLENBQUM7SUFFM0NDLGdEQUFTQSxDQUFDO1FBQ1Isb0VBQW9FO1FBQ3BFLE1BQU1hLFlBQVk7WUFDaEIsSUFBSTtnQkFDRixNQUFNQyxRQUFRQyxhQUFhQyxPQUFPLENBQUM7Z0JBQ25DLE1BQU1DLFdBQVdGLGFBQWFDLE9BQU8sQ0FBQztnQkFFdENFLFFBQVFDLEdBQUcsQ0FBQyw0QkFBNEI7b0JBQUVDLFVBQVUsQ0FBQyxDQUFDTjtvQkFBT08sYUFBYSxDQUFDLENBQUNKO2dCQUFTO2dCQUVyRixJQUFJSCxTQUFTRyxVQUFVO29CQUNyQixJQUFJO3dCQUNGLE1BQU1LLFdBQVdDLEtBQUtDLEtBQUssQ0FBQ1A7d0JBQzVCQyxRQUFRQyxHQUFHLENBQUMsb0NBQW9DRzt3QkFDaERaLFFBQVFZO29CQUNWLEVBQUUsT0FBT0csT0FBTzt3QkFDZFAsUUFBUU8sS0FBSyxDQUFDLDRCQUE0QkE7d0JBQzFDVixhQUFhVyxVQUFVLENBQUM7d0JBQ3hCWCxhQUFhVyxVQUFVLENBQUM7b0JBQzFCO2dCQUNGLE9BQU87b0JBQ0xSLFFBQVFDLEdBQUcsQ0FBQztnQkFDZDtZQUNGLEVBQUUsT0FBT00sT0FBTztnQkFDZFAsUUFBUU8sS0FBSyxDQUFDLGtDQUFrQ0E7Z0JBQ2hEVixhQUFhVyxVQUFVLENBQUM7Z0JBQ3hCWCxhQUFhVyxVQUFVLENBQUM7WUFDMUIsU0FBVTtnQkFDUmQsYUFBYTtZQUNmO1FBQ0Y7UUFFQSxzREFBc0Q7UUFDdERlLFdBQVdkLFdBQVc7SUFDeEIsR0FBRyxFQUFFO0lBRUwsTUFBTWUsUUFBUSxPQUFPQyxPQUFlQztRQUNsQyxJQUFJO1lBQ0ZsQixhQUFhO1lBQ2JNLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUJVO1lBRXJDLE1BQU1FLFdBQVcsTUFBTTlCLHFEQUFVQSxDQUFDMkIsS0FBSyxDQUFDQyxPQUFPQztZQUMvQ1osUUFBUUMsR0FBRyxDQUFDLG1CQUFtQlk7WUFFL0IsSUFBSUEsU0FBU0MsT0FBTyxJQUFJRCxTQUFTRSxJQUFJLEVBQUU7Z0JBQ3JDLCtEQUErRDtnQkFDL0QsTUFBTW5CLFFBQVFpQixTQUFTRSxJQUFJLENBQUNDLFlBQVk7Z0JBRXhDLElBQUlwQixNQUFNcUIsVUFBVSxDQUFDLGdCQUFnQjtvQkFDbkMsb0NBQW9DO29CQUNwQyxNQUFNQyxXQUFXO3dCQUNmQyxJQUFJO3dCQUNKQyxNQUFNO3dCQUNOVCxPQUFPQTt3QkFDUFUsT0FBTzt3QkFDUEMsWUFBWSxJQUFJQyxPQUFPQyxXQUFXO29CQUNwQztvQkFFQWhDLFFBQVEwQjtvQkFDUnJCLGFBQWE0QixPQUFPLENBQUMsYUFBYXBCLEtBQUtxQixTQUFTLENBQUNSO29CQUVqRCxPQUFPO3dCQUFFSixTQUFTO3dCQUFNYSxTQUFTZCxTQUFTYyxPQUFPLElBQUk7b0JBQStCO2dCQUN0RixPQUFPO29CQUNMLGdEQUFnRDtvQkFDaEQsSUFBSTt3QkFDRixNQUFNQyxlQUFlLE1BQU1DLE1BQU0sR0FBOEQsT0FBM0RDLE9BQU9BLENBQUNDLEdBQUcsQ0FBQ0MsbUJBQW1CLElBQUkseUJBQXdCLGlCQUFlOzRCQUM1R0MsU0FBUztnQ0FDUCxpQkFBaUIsVUFBZ0IsT0FBTnJDO2dDQUMzQixnQkFBZ0I7NEJBQ2xCO3dCQUNGO3dCQUVBLElBQUlnQyxhQUFhTSxFQUFFLEVBQUU7NEJBQ25CLE1BQU05QixXQUFXLE1BQU13QixhQUFhTyxJQUFJOzRCQUN4Q25DLFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0JHOzRCQUVwQ1osUUFBUVk7NEJBQ1JQLGFBQWE0QixPQUFPLENBQUMsYUFBYXBCLEtBQUtxQixTQUFTLENBQUN0Qjs0QkFFakQsT0FBTztnQ0FBRVUsU0FBUztnQ0FBTWEsU0FBUzs0QkFBbUI7d0JBQ3RELE9BQU87NEJBQ0wzQixRQUFRTyxLQUFLLENBQUM7NEJBQ2QsT0FBTztnQ0FBRU8sU0FBUztnQ0FBT2EsU0FBUzs0QkFBaUM7d0JBQ3JFO29CQUNGLEVBQUUsT0FBT3BCLE9BQU87d0JBQ2RQLFFBQVFPLEtBQUssQ0FBQyw0QkFBNEJBO3dCQUMxQyxPQUFPOzRCQUFFTyxTQUFTOzRCQUFPYSxTQUFTO3dCQUFpQztvQkFDckU7Z0JBQ0Y7WUFDRixPQUFPO2dCQUNMM0IsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQlksU0FBU2MsT0FBTztnQkFDN0MsT0FBTztvQkFBRWIsU0FBUztvQkFBT2EsU0FBU2QsU0FBU2MsT0FBTyxJQUFJO2dCQUFlO1lBQ3ZFO1FBQ0YsRUFBRSxPQUFPcEIsT0FBTztZQUNkUCxRQUFRTyxLQUFLLENBQUMsZ0JBQWdCQTtZQUM5QixPQUFPO2dCQUFFTyxTQUFTO2dCQUFPYSxTQUFTO1lBQTZCO1FBQ2pFLFNBQVU7WUFDUmpDLGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTTBDLFdBQVcsT0FBT2hDO1FBQ3RCLElBQUk7WUFDRlYsYUFBYTtZQUNiLE1BQU1tQixXQUFXLE1BQU05QixxREFBVUEsQ0FBQ3FELFFBQVEsQ0FBQ2hDO1lBRTNDLElBQUlTLFNBQVNDLE9BQU8sRUFBRTtnQkFDcEIsT0FBTztvQkFBRUEsU0FBUztvQkFBTWEsU0FBUztnQkFBeUM7WUFDNUUsT0FBTztnQkFDTCxPQUFPO29CQUFFYixTQUFTO29CQUFPYSxTQUFTZCxTQUFTYyxPQUFPLElBQUk7Z0JBQXNCO1lBQzlFO1FBQ0YsRUFBRSxPQUFPcEIsT0FBTztZQUNkUCxRQUFRTyxLQUFLLENBQUMsdUJBQXVCQTtZQUNyQyxPQUFPO2dCQUFFTyxTQUFTO2dCQUFPYSxTQUFTO1lBQW9DO1FBQ3hFLFNBQVU7WUFDUmpDLGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTTJDLFNBQVM7UUFDYjdDLFFBQVE7UUFDUlQscURBQVVBLENBQUNzRCxNQUFNO1FBQ2pCeEMsYUFBYVcsVUFBVSxDQUFDO0lBQzFCO0lBRUEsTUFBTThCLFFBQXlCO1FBQzdCL0M7UUFDQWdELGlCQUFpQixDQUFDLENBQUNoRDtRQUNuQkU7UUFDQWlCO1FBQ0EwQjtRQUNBQztJQUNGO0lBRUEscUJBQ0UsOERBQUNyRCxZQUFZd0QsUUFBUTtRQUFDRixPQUFPQTtrQkFDMUJoRDs7Ozs7O0FBR1AsRUFBRTtJQTlJV0Q7S0FBQUE7QUFnSmIsK0NBQStDO0FBQ3hDLE1BQU1vRCxXQUFXLENBQW1CQzs7SUFDekMsVUFBTyxTQUFTQyx1QkFBdUJDLEtBQVE7O1FBQzdDLE1BQU0sRUFBRUwsZUFBZSxFQUFFOUMsU0FBUyxFQUFFLEdBQUdQO1FBRXZDLElBQUlPLFdBQVc7WUFDYixxQkFDRSw4REFBQ29EO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7UUFHckI7UUFFQSxJQUFJLENBQUNQLGlCQUFpQjtZQUNwQixxQkFDRSw4REFBQ007Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNDO2dDQUFHRCxXQUFVOzBDQUE2Qzs7Ozs7OzBDQUczRCw4REFBQ0U7Z0NBQUVGLFdBQVU7MENBQTZCOzs7Ozs7MENBRzFDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0c7b0NBQ0NDLE1BQUs7b0NBQ0xKLFdBQVU7OENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztRQVFiO1FBRUEscUJBQU8sOERBQUNKO1lBQVcsR0FBR0UsS0FBSzs7Ozs7O0lBQzdCOztZQXBDeUMxRDs7O0FBcUMzQyxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb250ZXh0cy9BdXRoQ29udGV4dC50c3g/MWZhMiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlRWZmZWN0LCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBhcGlTZXJ2aWNlIH0gZnJvbSAnLi4vc2VydmljZXMvYXBpJztcblxuaW50ZXJmYWNlIFVzZXIge1xuICBpZDogbnVtYmVyO1xuICBuYW1lOiBzdHJpbmc7XG4gIGVtYWlsOiBzdHJpbmc7XG4gIHBob25lPzogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgQXV0aENvbnRleHRUeXBlIHtcbiAgdXNlcjogVXNlciB8IG51bGw7XG4gIGlzQXV0aGVudGljYXRlZDogYm9vbGVhbjtcbiAgaXNMb2FkaW5nOiBib29sZWFuO1xuICBsb2dpbjogKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpID0+IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBtZXNzYWdlPzogc3RyaW5nIH0+O1xuICByZWdpc3RlcjogKHVzZXJEYXRhOiB7IG5hbWU6IHN0cmluZzsgZW1haWw6IHN0cmluZzsgcGFzc3dvcmQ6IHN0cmluZzsgcGhvbmU/OiBzdHJpbmcgfSkgPT4gUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IG1lc3NhZ2U/OiBzdHJpbmcgfT47XG4gIGxvZ291dDogKCkgPT4gdm9pZDtcbn1cblxuY29uc3QgQXV0aENvbnRleHQgPSBjcmVhdGVDb250ZXh0PEF1dGhDb250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKTtcblxuZXhwb3J0IGNvbnN0IHVzZUF1dGggPSAoKSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEF1dGhDb250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXV0aCBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEF1dGhQcm92aWRlcicpO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufTtcblxuaW50ZXJmYWNlIEF1dGhQcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGNvbnN0IEF1dGhQcm92aWRlcjogUmVhY3QuRkM8QXV0aFByb3ZpZGVyUHJvcHM+ID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxVc2VyIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFNpbXBsaWZpZWQgYXV0aCBjaGVjayAtIGp1c3QgY2hlY2sgbG9jYWxTdG9yYWdlIHdpdGhvdXQgQVBJIGNhbGxzXG4gICAgY29uc3QgY2hlY2tBdXRoID0gKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYXV0aF90b2tlbicpO1xuICAgICAgICBjb25zdCB1c2VySW5mbyA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VyX2luZm8nKTtcblxuICAgICAgICBjb25zb2xlLmxvZygnQ2hlY2tpbmcgYXV0aGVudGljYXRpb246JywgeyBoYXNUb2tlbjogISF0b2tlbiwgaGFzVXNlckluZm86ICEhdXNlckluZm8gfSk7XG5cbiAgICAgICAgaWYgKHRva2VuICYmIHVzZXJJbmZvKSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHVzZXJEYXRhID0gSlNPTi5wYXJzZSh1c2VySW5mbyk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygnVXNlciBkYXRhIGZvdW5kIGluIGxvY2FsU3RvcmFnZTonLCB1c2VyRGF0YSk7XG4gICAgICAgICAgICBzZXRVc2VyKHVzZXJEYXRhKTtcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcGFyc2luZyB1c2VyIGluZm86JywgZXJyb3IpO1xuICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2F1dGhfdG9rZW4nKTtcbiAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyX2luZm8nKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ05vIGF1dGhlbnRpY2F0aW9uIGRhdGEgZm91bmQnKTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2hlY2tpbmcgYXV0aGVudGljYXRpb246JywgZXJyb3IpO1xuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYXV0aF90b2tlbicpO1xuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndXNlcl9pbmZvJyk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICAvLyBVc2Ugc2V0VGltZW91dCB0byBhdm9pZCBibG9ja2luZyB0aGUgaW5pdGlhbCByZW5kZXJcbiAgICBzZXRUaW1lb3V0KGNoZWNrQXV0aCwgMTAwKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGxvZ2luID0gYXN5bmMgKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgbWVzc2FnZT86IHN0cmluZyB9PiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnNvbGUubG9nKCdBdHRlbXB0aW5nIGxvZ2luIGZvcjonLCBlbWFpbCk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpU2VydmljZS5sb2dpbihlbWFpbCwgcGFzc3dvcmQpO1xuICAgICAgY29uc29sZS5sb2coJ0xvZ2luIHJlc3BvbnNlOicsIHJlc3BvbnNlKTtcblxuICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MgJiYgcmVzcG9uc2UuZGF0YSkge1xuICAgICAgICAvLyBDaGVjayBpZiB0aGlzIGlzIGRlbW8gbW9kZSAodG9rZW4gc3RhcnRzIHdpdGggJ2RlbW9fdG9rZW5fJylcbiAgICAgICAgY29uc3QgdG9rZW4gPSByZXNwb25zZS5kYXRhLmFjY2Vzc190b2tlbjtcblxuICAgICAgICBpZiAodG9rZW4uc3RhcnRzV2l0aCgnZGVtb190b2tlbl8nKSkge1xuICAgICAgICAgIC8vIERlbW8gbW9kZSAtIGNyZWF0ZSBtb2NrIHVzZXIgZGF0YVxuICAgICAgICAgIGNvbnN0IG1vY2tVc2VyID0ge1xuICAgICAgICAgICAgaWQ6IDEsXG4gICAgICAgICAgICBuYW1lOiAnRGVtbyBVc2VyJyxcbiAgICAgICAgICAgIGVtYWlsOiBlbWFpbCxcbiAgICAgICAgICAgIHBob25lOiAnKzkxLTk4NzY1NDMyMTAnLFxuICAgICAgICAgICAgY3JlYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgfTtcblxuICAgICAgICAgIHNldFVzZXIobW9ja1VzZXIpO1xuICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd1c2VyX2luZm8nLCBKU09OLnN0cmluZ2lmeShtb2NrVXNlcikpO1xuXG4gICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgbWVzc2FnZTogcmVzcG9uc2UubWVzc2FnZSB8fCAnTG9naW4gc3VjY2Vzc2Z1bCAoRGVtbyBNb2RlKScgfTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyBSZWFsIGJhY2tlbmQgbW9kZSAtIGdldCB1c2VyIGluZm8gZnJvbSBzZXJ2ZXJcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgdXNlclJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDo4MDAwJ30vYXBpL2F1dGgvbWVgLCB7XG4gICAgICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gLFxuICAgICAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgIGlmICh1c2VyUmVzcG9uc2Uub2spIHtcbiAgICAgICAgICAgICAgY29uc3QgdXNlckRhdGEgPSBhd2FpdCB1c2VyUmVzcG9uc2UuanNvbigpO1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnVXNlciBkYXRhIHJldHJpZXZlZDonLCB1c2VyRGF0YSk7XG5cbiAgICAgICAgICAgICAgc2V0VXNlcih1c2VyRGF0YSk7XG4gICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd1c2VyX2luZm8nLCBKU09OLnN0cmluZ2lmeSh1c2VyRGF0YSkpO1xuXG4gICAgICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIG1lc3NhZ2U6ICdMb2dpbiBzdWNjZXNzZnVsJyB9O1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGdldCB1c2VyIGluZm8gYWZ0ZXIgbG9naW4nKTtcbiAgICAgICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIG1lc3NhZ2U6ICdGYWlsZWQgdG8gZ2V0IHVzZXIgaW5mb3JtYXRpb24nIH07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgdXNlciBpbmZvOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBtZXNzYWdlOiAnRmFpbGVkIHRvIGdldCB1c2VyIGluZm9ybWF0aW9uJyB9O1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0xvZ2luIGZhaWxlZDonLCByZXNwb25zZS5tZXNzYWdlKTtcbiAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIG1lc3NhZ2U6IHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ0xvZ2luIGZhaWxlZCcgfTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignTG9naW4gZXJyb3I6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIG1lc3NhZ2U6ICdOZXR3b3JrIGVycm9yIGR1cmluZyBsb2dpbicgfTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgcmVnaXN0ZXIgPSBhc3luYyAodXNlckRhdGE6IHsgbmFtZTogc3RyaW5nOyBlbWFpbDogc3RyaW5nOyBwYXNzd29yZDogc3RyaW5nOyBwaG9uZT86IHN0cmluZyB9KTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IG1lc3NhZ2U/OiBzdHJpbmcgfT4gPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaVNlcnZpY2UucmVnaXN0ZXIodXNlckRhdGEpO1xuICAgICAgXG4gICAgICBpZiAocmVzcG9uc2Uuc3VjY2Vzcykge1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBtZXNzYWdlOiAnUmVnaXN0cmF0aW9uIHN1Y2Nlc3NmdWwuIFBsZWFzZSBsb2dpbi4nIH07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgbWVzc2FnZTogcmVzcG9uc2UubWVzc2FnZSB8fCAnUmVnaXN0cmF0aW9uIGZhaWxlZCcgfTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignUmVnaXN0cmF0aW9uIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBtZXNzYWdlOiAnTmV0d29yayBlcnJvciBkdXJpbmcgcmVnaXN0cmF0aW9uJyB9O1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBsb2dvdXQgPSAoKSA9PiB7XG4gICAgc2V0VXNlcihudWxsKTtcbiAgICBhcGlTZXJ2aWNlLmxvZ291dCgpO1xuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyX2luZm8nKTtcbiAgfTtcblxuICBjb25zdCB2YWx1ZTogQXV0aENvbnRleHRUeXBlID0ge1xuICAgIHVzZXIsXG4gICAgaXNBdXRoZW50aWNhdGVkOiAhIXVzZXIsXG4gICAgaXNMb2FkaW5nLFxuICAgIGxvZ2luLFxuICAgIHJlZ2lzdGVyLFxuICAgIGxvZ291dFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPEF1dGhDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9BdXRoQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn07XG5cbi8vIEhpZ2hlci1vcmRlciBjb21wb25lbnQgZm9yIHByb3RlY3Rpbmcgcm91dGVzXG5leHBvcnQgY29uc3Qgd2l0aEF1dGggPSA8UCBleHRlbmRzIG9iamVjdD4oQ29tcG9uZW50OiBSZWFjdC5Db21wb25lbnRUeXBlPFA+KSA9PiB7XG4gIHJldHVybiBmdW5jdGlvbiBBdXRoZW50aWNhdGVkQ29tcG9uZW50KHByb3BzOiBQKSB7XG4gICAgY29uc3QgeyBpc0F1dGhlbnRpY2F0ZWQsIGlzTG9hZGluZyB9ID0gdXNlQXV0aCgpO1xuXG4gICAgaWYgKGlzTG9hZGluZykge1xuICAgICAgcmV0dXJuIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0zMiB3LTMyIGJvcmRlci1iLTIgYm9yZGVyLWluZGlnby02MDBcIj48L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApO1xuICAgIH1cblxuICAgIGlmICghaXNBdXRoZW50aWNhdGVkKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy1tZCB3LWZ1bGwgc3BhY2UteS04XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJtdC02IHRleHQtM3hsIGZvbnQtZXh0cmFib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICBBdXRoZW50aWNhdGlvbiBSZXF1aXJlZFxuICAgICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0yIHRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIFBsZWFzZSBsb2dpbiB0byBhY2Nlc3MgdGhpcyBwYWdlXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00XCI+XG4gICAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICAgIGhyZWY9XCIvYXV0aC9sb2dpblwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIGJvcmRlciBib3JkZXItdHJhbnNwYXJlbnQgdGV4dC1zbSBmb250LW1lZGl1bSByb3VuZGVkLW1kIHRleHQtd2hpdGUgYmctaW5kaWdvLTYwMCBob3ZlcjpiZy1pbmRpZ28tNzAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBHbyB0byBMb2dpblxuICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApO1xuICAgIH1cblxuICAgIHJldHVybiA8Q29tcG9uZW50IHsuLi5wcm9wc30gLz47XG4gIH07XG59O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImFwaVNlcnZpY2UiLCJBdXRoQ29udGV4dCIsInVuZGVmaW5lZCIsInVzZUF1dGgiLCJjb250ZXh0IiwiRXJyb3IiLCJBdXRoUHJvdmlkZXIiLCJjaGlsZHJlbiIsInVzZXIiLCJzZXRVc2VyIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiY2hlY2tBdXRoIiwidG9rZW4iLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwidXNlckluZm8iLCJjb25zb2xlIiwibG9nIiwiaGFzVG9rZW4iLCJoYXNVc2VySW5mbyIsInVzZXJEYXRhIiwiSlNPTiIsInBhcnNlIiwiZXJyb3IiLCJyZW1vdmVJdGVtIiwic2V0VGltZW91dCIsImxvZ2luIiwiZW1haWwiLCJwYXNzd29yZCIsInJlc3BvbnNlIiwic3VjY2VzcyIsImRhdGEiLCJhY2Nlc3NfdG9rZW4iLCJzdGFydHNXaXRoIiwibW9ja1VzZXIiLCJpZCIsIm5hbWUiLCJwaG9uZSIsImNyZWF0ZWRfYXQiLCJEYXRlIiwidG9JU09TdHJpbmciLCJzZXRJdGVtIiwic3RyaW5naWZ5IiwibWVzc2FnZSIsInVzZXJSZXNwb25zZSIsImZldGNoIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJoZWFkZXJzIiwib2siLCJqc29uIiwicmVnaXN0ZXIiLCJsb2dvdXQiLCJ2YWx1ZSIsImlzQXV0aGVudGljYXRlZCIsIlByb3ZpZGVyIiwid2l0aEF1dGgiLCJDb21wb25lbnQiLCJBdXRoZW50aWNhdGVkQ29tcG9uZW50IiwicHJvcHMiLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsInAiLCJhIiwiaHJlZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiService: function() { return /* binding */ apiService; }\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\n * API Service for Niveshtor Trading Application\n * Handles all API calls with proper authentication and error handling\n */ const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\";\nclass ApiService {\n    getAuthHeaders() {\n        const token = localStorage.getItem(\"auth_token\");\n        return {\n            \"Content-Type\": \"application/json\",\n            ...token && {\n                \"Authorization\": \"Bearer \".concat(token)\n            }\n        };\n    }\n    async handleResponse(response) {\n        try {\n            const data = await response.json();\n            if (response.ok) {\n                return {\n                    success: true,\n                    data: data,\n                    message: data.message\n                };\n            } else {\n                // Handle different error formats\n                const errorMessage = data.detail || data.message || \"HTTP \".concat(response.status, \": \").concat(response.statusText);\n                return {\n                    success: false,\n                    error: typeof errorMessage === \"string\" ? errorMessage : JSON.stringify(errorMessage),\n                    message: typeof errorMessage === \"string\" ? errorMessage : \"An error occurred\"\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                error: \"Failed to parse response\",\n                message: \"Network or parsing error occurred\"\n            };\n        }\n    }\n    // Authentication APIs\n    async login(email, password) {\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/auth/login\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const result = await this.handleResponse(response);\n            if (result.success && result.data) {\n                localStorage.setItem(\"auth_token\", result.data.access_token);\n            }\n            return result;\n        } catch (error) {\n            console.log(\"Backend server not available, using demo mode\");\n            // Demo mode: Check for demo credentials\n            if (email === \"<EMAIL>\" && password === \"demo123\") {\n                return new Promise((resolve)=>{\n                    setTimeout(()=>{\n                        const mockToken = \"demo_token_\" + Date.now();\n                        localStorage.setItem(\"auth_token\", mockToken);\n                        resolve({\n                            success: true,\n                            message: \"Login successful! (Demo Mode)\",\n                            data: {\n                                access_token: mockToken,\n                                token_type: \"Bearer\"\n                            }\n                        });\n                    }, 800); // Simulate network delay\n                });\n            } else {\n                return {\n                    success: false,\n                    error: \"Invalid credentials\",\n                    message: \"Invalid email or password. Try <EMAIL> / demo123 (Demo Mode - Backend server not running)\"\n                };\n            }\n        }\n    }\n    async register(userData) {\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/auth/register\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(userData)\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            console.log(\"Backend server not available, using demo mode\");\n            // Demo mode: Simulate successful registration\n            return new Promise((resolve)=>{\n                setTimeout(()=>{\n                    resolve({\n                        success: true,\n                        message: \"Account created successfully! (Demo Mode - Backend server not running)\",\n                        data: {\n                            user: {\n                                id: Date.now(),\n                                name: userData.name,\n                                email: userData.email,\n                                phone: userData.phone\n                            }\n                        }\n                    });\n                }, 1000); // Simulate network delay\n            });\n        }\n    }\n    logout() {\n        localStorage.removeItem(\"auth_token\");\n    }\n    // Broker Connection APIs\n    async createBrokerConnection(connectionData) {\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/broker/connections\"), {\n                method: \"POST\",\n                headers: this.getAuthHeaders(),\n                body: JSON.stringify(connectionData)\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            return {\n                success: false,\n                error: \"Network error\",\n                message: \"Failed to connect to server\"\n            };\n        }\n    }\n    async getBrokerConnections() {\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/broker/connections\"), {\n                method: \"GET\",\n                headers: this.getAuthHeaders()\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            return {\n                success: false,\n                error: \"Network error\",\n                message: \"Failed to connect to server\"\n            };\n        }\n    }\n    async manageBrokerConnection(connectionId, action) {\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/broker/connections/\").concat(connectionId, \"/action\"), {\n                method: \"POST\",\n                headers: this.getAuthHeaders(),\n                body: JSON.stringify({\n                    connection_id: connectionId,\n                    action\n                })\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            return {\n                success: false,\n                error: \"Network error\",\n                message: \"Failed to connect to server\"\n            };\n        }\n    }\n    async deleteBrokerConnection(connectionId) {\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/broker/connections/\").concat(connectionId), {\n                method: \"DELETE\",\n                headers: this.getAuthHeaders()\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            return {\n                success: false,\n                error: \"Network error\",\n                message: \"Failed to connect to server\"\n            };\n        }\n    }\n    async getConnectionStats() {\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/broker/connection-stats\"), {\n                method: \"GET\",\n                headers: this.getAuthHeaders()\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            return {\n                success: false,\n                error: \"Network error\",\n                message: \"Failed to connect to server\"\n            };\n        }\n    }\n    async getMarketData() {\n        let symbols = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"RELIANCE,TCS,HDFC,INFY\";\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/api/broker/market-data?symbols=\").concat(symbols), {\n                method: \"GET\",\n                headers: this.getAuthHeaders()\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            return {\n                success: false,\n                error: \"Network error\",\n                message: \"Failed to connect to server\"\n            };\n        }\n    }\n    // Utility method to check if user is authenticated\n    isAuthenticated() {\n        return !!localStorage.getItem(\"auth_token\");\n    }\n    // Utility method to get current user info (if stored)\n    getCurrentUser() {\n        const userInfo = localStorage.getItem(\"user_info\");\n        return userInfo ? JSON.parse(userInfo) : null;\n    }\n}\n// Export singleton instance\nconst apiService = new ApiService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/api.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/compiled/process/browser.js ***!
  \************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(function(){var e={229:function(e){var t=e.exports={};var r;var n;function defaultSetTimout(){throw new Error(\"setTimeout has not been defined\")}function defaultClearTimeout(){throw new Error(\"clearTimeout has not been defined\")}(function(){try{if(typeof setTimeout===\"function\"){r=setTimeout}else{r=defaultSetTimout}}catch(e){r=defaultSetTimout}try{if(typeof clearTimeout===\"function\"){n=clearTimeout}else{n=defaultClearTimeout}}catch(e){n=defaultClearTimeout}})();function runTimeout(e){if(r===setTimeout){return setTimeout(e,0)}if((r===defaultSetTimout||!r)&&setTimeout){r=setTimeout;return setTimeout(e,0)}try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}function runClearTimeout(e){if(n===clearTimeout){return clearTimeout(e)}if((n===defaultClearTimeout||!n)&&clearTimeout){n=clearTimeout;return clearTimeout(e)}try{return n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}var i=[];var o=false;var u;var a=-1;function cleanUpNextTick(){if(!o||!u){return}o=false;if(u.length){i=u.concat(i)}else{a=-1}if(i.length){drainQueue()}}function drainQueue(){if(o){return}var e=runTimeout(cleanUpNextTick);o=true;var t=i.length;while(t){u=i;i=[];while(++a<t){if(u){u[a].run()}}a=-1;t=i.length}u=null;o=false;runClearTimeout(e)}t.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1){for(var r=1;r<arguments.length;r++){t[r-1]=arguments[r]}}i.push(new Item(e,t));if(i.length===1&&!o){runTimeout(drainQueue)}};function Item(e,t){this.fun=e;this.array=t}Item.prototype.run=function(){this.fun.apply(null,this.array)};t.title=\"browser\";t.browser=true;t.env={};t.argv=[];t.version=\"\";t.versions={};function noop(){}t.on=noop;t.addListener=noop;t.once=noop;t.off=noop;t.removeListener=noop;t.removeAllListeners=noop;t.emit=noop;t.prependListener=noop;t.prependOnceListener=noop;t.listeners=function(e){return[]};t.binding=function(e){throw new Error(\"process.binding is not supported\")};t.cwd=function(){return\"/\"};t.chdir=function(e){throw new Error(\"process.chdir is not supported\")};t.umask=function(){return 0}}};var t={};function __nccwpck_require__(r){var n=t[r];if(n!==undefined){return n.exports}var i=t[r]={exports:{}};var o=true;try{e[r](i,i.exports,__nccwpck_require__);o=false}finally{if(o)delete t[r]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var r=__nccwpck_require__(229);module.exports=r})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe[prop-missing]\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      } // TODO(luna): This will currently only throw if the function component\n      // tries to access React/ReactDOM/props. We should probably make this throw\n      // in simple components too\n\n\n      var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n      // component, which we don't yet support. Attach a noop catch handler to\n      // silence the error.\n      // TODO: Implement component stacks for async client components?\n\n      if (maybePromise && typeof maybePromise.catch === 'function') {\n        maybePromise.catch(function () {});\n      }\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe[incompatible-use] This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement$1(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement$1(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement$1(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement$1(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner$1.current && self && ReactCurrentOwner$1.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner$1.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner$1.current, props);\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    if (type.$$typeof === REACT_CLIENT_REFERENCE) {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar didWarnAboutKeySpread = {};\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV$1(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    if (hasOwnProperty.call(props, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(props).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n\nvar jsxDEV = jsxWithValidation ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWE7O0FBRWIsSUFBSSxJQUFxQztBQUN6QztBQUNBOztBQUVBLFlBQVksbUJBQU8sQ0FBQyxzR0FBMEI7O0FBRTlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxpR0FBaUcsZUFBZTtBQUNoSDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxNQUFNOzs7QUFHTjtBQUNBO0FBQ0EsS0FBSyxHQUFHOztBQUVSLGtEQUFrRDtBQUNsRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSw0QkFBNEI7QUFDNUI7QUFDQSxxQ0FBcUM7O0FBRXJDLGdDQUFnQztBQUNoQztBQUNBOztBQUVBLGdDQUFnQzs7QUFFaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJOzs7QUFHSjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLEVBQUU7OztBQUdGO0FBQ0E7QUFDQSxFQUFFOzs7QUFHRjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUM7O0FBRXZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTOztBQUVUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUzs7QUFFVDtBQUNBLHNCQUFzQjtBQUN0QjtBQUNBLFNBQVM7QUFDVCx1QkFBdUI7QUFDdkI7QUFDQSxTQUFTO0FBQ1QsdUJBQXVCO0FBQ3ZCO0FBQ0EsU0FBUztBQUNULHdCQUF3QjtBQUN4QjtBQUNBLFNBQVM7QUFDVCx3QkFBd0I7QUFDeEI7QUFDQSxTQUFTO0FBQ1QsaUNBQWlDO0FBQ2pDO0FBQ0EsU0FBUztBQUNULDJCQUEyQjtBQUMzQjtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsTUFBTTs7O0FBR047QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLDJEQUEyRDs7QUFFM0Q7QUFDQTs7QUFFQTtBQUNBLHlEQUF5RDtBQUN6RDs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUzs7O0FBR1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTzs7QUFFUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7O0FBRUE7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBLFVBQVU7OztBQUdWO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBLFFBQVE7QUFDUjtBQUNBOzs7QUFHQSwrQkFBK0I7QUFDL0I7QUFDQTtBQUNBOztBQUVBO0FBQ0EseUNBQXlDO0FBQ3pDO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsYUFBYSxrQkFBa0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjs7QUFFQTtBQUNBO0FBQ0EsZ0ZBQWdGO0FBQ2hGO0FBQ0E7OztBQUdBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjs7O0FBR2xCO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxJQUFJOzs7QUFHSjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLDhCQUE4QjtBQUM5QjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwySEFBMkg7QUFDM0g7QUFDQTtBQUNBOztBQUVBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSxvRUFBb0U7O0FBRXBFO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsaUNBQWlDOztBQUVqQztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtHQUFrRzs7QUFFbEc7QUFDQTtBQUNBLEVBQUU7OztBQUdGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHdDQUF3QztBQUN4QztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsR0FBRztBQUNkLFdBQVcsR0FBRztBQUNkLFdBQVcsR0FBRztBQUNkLFdBQVcsZUFBZTtBQUMxQixXQUFXLEdBQUc7QUFDZCxXQUFXLEdBQUc7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsR0FBRztBQUNkO0FBQ0E7QUFDQTs7O0FBR0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLLEdBQUc7O0FBRVI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUssR0FBRztBQUNSOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLOztBQUVMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLEdBQUc7QUFDZCxXQUFXLFFBQVE7QUFDbkIsV0FBVyxRQUFRO0FBQ25COztBQUVBO0FBQ0E7QUFDQSxrQkFBa0I7O0FBRWxCO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEIsMkRBQTJELFVBQVU7QUFDckUseUJBQXlCLFVBQVU7QUFDbkM7QUFDQSxhQUFhLFVBQVU7QUFDdkI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE1BQU07OztBQUdOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTs7O0FBR047QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLFlBQVksU0FBUztBQUNyQjtBQUNBOzs7QUFHQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOzs7QUFHQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxjQUFjO0FBQ3pCLFdBQVcsR0FBRztBQUNkOzs7QUFHQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLDZEQUE2RDtBQUM3RDtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsV0FBVztBQUN0QixXQUFXLEdBQUc7QUFDZDs7O0FBR0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxvREFBb0Q7QUFDcEQsc0JBQXNCLGlCQUFpQjtBQUN2Qzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsY0FBYztBQUN6Qjs7O0FBR0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTiw0Q0FBNEM7O0FBRTVDOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGNBQWM7QUFDekI7OztBQUdBO0FBQ0E7QUFDQTs7QUFFQSxvQkFBb0IsaUJBQWlCO0FBQ3JDOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBOEM7QUFDOUM7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsNERBQTREO0FBQzVEOztBQUVBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7OztBQUdBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLHFCQUFxQjtBQUNqRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLDhDQUE4QyxnREFBZ0QsTUFBTSxhQUFhOztBQUVqSDtBQUNBLCtDQUErQyxrQ0FBa0MsT0FBTzs7QUFFeEYsdUdBQXVHLGNBQWMsVUFBVSxnR0FBZ0csa0JBQWtCLFVBQVUsVUFBVTs7QUFFclE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTs7QUFFQTtBQUNBO0FBQ0EsRUFBRTs7QUFFRjs7QUFFQSxnQkFBZ0I7QUFDaEIsY0FBYztBQUNkLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanM/ZDg4NyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIFJlYWN0XG4gKiByZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanNcbiAqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAoZnVuY3Rpb24oKSB7XG4ndXNlIHN0cmljdCc7XG5cbnZhciBSZWFjdCA9IHJlcXVpcmUoXCJuZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3RcIik7XG5cbi8vIEFUVEVOVElPTlxuLy8gV2hlbiBhZGRpbmcgbmV3IHN5bWJvbHMgdG8gdGhpcyBmaWxlLFxuLy8gUGxlYXNlIGNvbnNpZGVyIGFsc28gYWRkaW5nIHRvICdyZWFjdC1kZXZ0b29scy1zaGFyZWQvc3JjL2JhY2tlbmQvUmVhY3RTeW1ib2xzJ1xuLy8gVGhlIFN5bWJvbCB1c2VkIHRvIHRhZyB0aGUgUmVhY3RFbGVtZW50LWxpa2UgdHlwZXMuXG52YXIgUkVBQ1RfRUxFTUVOVF9UWVBFID0gU3ltYm9sLmZvcigncmVhY3QuZWxlbWVudCcpO1xudmFyIFJFQUNUX1BPUlRBTF9UWVBFID0gU3ltYm9sLmZvcigncmVhY3QucG9ydGFsJyk7XG52YXIgUkVBQ1RfRlJBR01FTlRfVFlQRSA9IFN5bWJvbC5mb3IoJ3JlYWN0LmZyYWdtZW50Jyk7XG52YXIgUkVBQ1RfU1RSSUNUX01PREVfVFlQRSA9IFN5bWJvbC5mb3IoJ3JlYWN0LnN0cmljdF9tb2RlJyk7XG52YXIgUkVBQ1RfUFJPRklMRVJfVFlQRSA9IFN5bWJvbC5mb3IoJ3JlYWN0LnByb2ZpbGVyJyk7XG52YXIgUkVBQ1RfUFJPVklERVJfVFlQRSA9IFN5bWJvbC5mb3IoJ3JlYWN0LnByb3ZpZGVyJyk7XG52YXIgUkVBQ1RfQ09OVEVYVF9UWVBFID0gU3ltYm9sLmZvcigncmVhY3QuY29udGV4dCcpO1xudmFyIFJFQUNUX0ZPUldBUkRfUkVGX1RZUEUgPSBTeW1ib2wuZm9yKCdyZWFjdC5mb3J3YXJkX3JlZicpO1xudmFyIFJFQUNUX1NVU1BFTlNFX1RZUEUgPSBTeW1ib2wuZm9yKCdyZWFjdC5zdXNwZW5zZScpO1xudmFyIFJFQUNUX1NVU1BFTlNFX0xJU1RfVFlQRSA9IFN5bWJvbC5mb3IoJ3JlYWN0LnN1c3BlbnNlX2xpc3QnKTtcbnZhciBSRUFDVF9NRU1PX1RZUEUgPSBTeW1ib2wuZm9yKCdyZWFjdC5tZW1vJyk7XG52YXIgUkVBQ1RfTEFaWV9UWVBFID0gU3ltYm9sLmZvcigncmVhY3QubGF6eScpO1xudmFyIFJFQUNUX09GRlNDUkVFTl9UWVBFID0gU3ltYm9sLmZvcigncmVhY3Qub2Zmc2NyZWVuJyk7XG52YXIgUkVBQ1RfQ0FDSEVfVFlQRSA9IFN5bWJvbC5mb3IoJ3JlYWN0LmNhY2hlJyk7XG52YXIgTUFZQkVfSVRFUkFUT1JfU1lNQk9MID0gU3ltYm9sLml0ZXJhdG9yO1xudmFyIEZBVVhfSVRFUkFUT1JfU1lNQk9MID0gJ0BAaXRlcmF0b3InO1xuZnVuY3Rpb24gZ2V0SXRlcmF0b3JGbihtYXliZUl0ZXJhYmxlKSB7XG4gIGlmIChtYXliZUl0ZXJhYmxlID09PSBudWxsIHx8IHR5cGVvZiBtYXliZUl0ZXJhYmxlICE9PSAnb2JqZWN0Jykge1xuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgdmFyIG1heWJlSXRlcmF0b3IgPSBNQVlCRV9JVEVSQVRPUl9TWU1CT0wgJiYgbWF5YmVJdGVyYWJsZVtNQVlCRV9JVEVSQVRPUl9TWU1CT0xdIHx8IG1heWJlSXRlcmFibGVbRkFVWF9JVEVSQVRPUl9TWU1CT0xdO1xuXG4gIGlmICh0eXBlb2YgbWF5YmVJdGVyYXRvciA9PT0gJ2Z1bmN0aW9uJykge1xuICAgIHJldHVybiBtYXliZUl0ZXJhdG9yO1xuICB9XG5cbiAgcmV0dXJuIG51bGw7XG59XG5cbnZhciBSZWFjdFNoYXJlZEludGVybmFscyA9IFJlYWN0Ll9fU0VDUkVUX0lOVEVSTkFMU19ET19OT1RfVVNFX09SX1lPVV9XSUxMX0JFX0ZJUkVEO1xuXG5mdW5jdGlvbiBlcnJvcihmb3JtYXQpIHtcbiAge1xuICAgIHtcbiAgICAgIGZvciAodmFyIF9sZW4yID0gYXJndW1lbnRzLmxlbmd0aCwgYXJncyA9IG5ldyBBcnJheShfbGVuMiA+IDEgPyBfbGVuMiAtIDEgOiAwKSwgX2tleTIgPSAxOyBfa2V5MiA8IF9sZW4yOyBfa2V5MisrKSB7XG4gICAgICAgIGFyZ3NbX2tleTIgLSAxXSA9IGFyZ3VtZW50c1tfa2V5Ml07XG4gICAgICB9XG5cbiAgICAgIHByaW50V2FybmluZygnZXJyb3InLCBmb3JtYXQsIGFyZ3MpO1xuICAgIH1cbiAgfVxufVxuXG5mdW5jdGlvbiBwcmludFdhcm5pbmcobGV2ZWwsIGZvcm1hdCwgYXJncykge1xuICAvLyBXaGVuIGNoYW5naW5nIHRoaXMgbG9naWMsIHlvdSBtaWdodCB3YW50IHRvIGFsc29cbiAgLy8gdXBkYXRlIGNvbnNvbGVXaXRoU3RhY2tEZXYud3d3LmpzIGFzIHdlbGwuXG4gIHtcbiAgICB2YXIgUmVhY3REZWJ1Z0N1cnJlbnRGcmFtZSA9IFJlYWN0U2hhcmVkSW50ZXJuYWxzLlJlYWN0RGVidWdDdXJyZW50RnJhbWU7XG4gICAgdmFyIHN0YWNrID0gUmVhY3REZWJ1Z0N1cnJlbnRGcmFtZS5nZXRTdGFja0FkZGVuZHVtKCk7XG5cbiAgICBpZiAoc3RhY2sgIT09ICcnKSB7XG4gICAgICBmb3JtYXQgKz0gJyVzJztcbiAgICAgIGFyZ3MgPSBhcmdzLmNvbmNhdChbc3RhY2tdKTtcbiAgICB9IC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC1pbnRlcm5hbC9zYWZlLXN0cmluZy1jb2VyY2lvblxuXG5cbiAgICB2YXIgYXJnc1dpdGhGb3JtYXQgPSBhcmdzLm1hcChmdW5jdGlvbiAoaXRlbSkge1xuICAgICAgcmV0dXJuIFN0cmluZyhpdGVtKTtcbiAgICB9KTsgLy8gQ2FyZWZ1bDogUk4gY3VycmVudGx5IGRlcGVuZHMgb24gdGhpcyBwcmVmaXhcblxuICAgIGFyZ3NXaXRoRm9ybWF0LnVuc2hpZnQoJ1dhcm5pbmc6ICcgKyBmb3JtYXQpOyAvLyBXZSBpbnRlbnRpb25hbGx5IGRvbid0IHVzZSBzcHJlYWQgKG9yIC5hcHBseSkgZGlyZWN0bHkgYmVjYXVzZSBpdFxuICAgIC8vIGJyZWFrcyBJRTk6IGh0dHBzOi8vZ2l0aHViLmNvbS9mYWNlYm9vay9yZWFjdC9pc3N1ZXMvMTM2MTBcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaW50ZXJuYWwvbm8tcHJvZHVjdGlvbi1sb2dnaW5nXG5cbiAgICBGdW5jdGlvbi5wcm90b3R5cGUuYXBwbHkuY2FsbChjb25zb2xlW2xldmVsXSwgY29uc29sZSwgYXJnc1dpdGhGb3JtYXQpO1xuICB9XG59XG5cbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbnZhciBlbmFibGVTY29wZUFQSSA9IGZhbHNlOyAvLyBFeHBlcmltZW50YWwgQ3JlYXRlIEV2ZW50IEhhbmRsZSBBUEkuXG52YXIgZW5hYmxlQ2FjaGVFbGVtZW50ID0gZmFsc2U7XG52YXIgZW5hYmxlVHJhbnNpdGlvblRyYWNpbmcgPSBmYWxzZTsgLy8gTm8ga25vd24gYnVncywgYnV0IG5lZWRzIHBlcmZvcm1hbmNlIHRlc3RpbmdcblxudmFyIGVuYWJsZUxlZ2FjeUhpZGRlbiA9IGZhbHNlOyAvLyBFbmFibGVzIHVuc3RhYmxlX2F2b2lkVGhpc0ZhbGxiYWNrIGZlYXR1cmUgaW4gRmliZXJcbi8vIHN0dWZmLiBJbnRlbmRlZCB0byBlbmFibGUgUmVhY3QgY29yZSBtZW1iZXJzIHRvIG1vcmUgZWFzaWx5IGRlYnVnIHNjaGVkdWxpbmdcbi8vIGlzc3VlcyBpbiBERVYgYnVpbGRzLlxuXG52YXIgZW5hYmxlRGVidWdUcmFjaW5nID0gZmFsc2U7IC8vIFRyYWNrIHdoaWNoIEZpYmVyKHMpIHNjaGVkdWxlIHJlbmRlciB3b3JrLlxuXG52YXIgUkVBQ1RfQ0xJRU5UX1JFRkVSRU5DRSQxID0gU3ltYm9sLmZvcigncmVhY3QuY2xpZW50LnJlZmVyZW5jZScpO1xuZnVuY3Rpb24gaXNWYWxpZEVsZW1lbnRUeXBlKHR5cGUpIHtcbiAgaWYgKHR5cGVvZiB0eXBlID09PSAnc3RyaW5nJyB8fCB0eXBlb2YgdHlwZSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgIHJldHVybiB0cnVlO1xuICB9IC8vIE5vdGU6IHR5cGVvZiBtaWdodCBiZSBvdGhlciB0aGFuICdzeW1ib2wnIG9yICdudW1iZXInIChlLmcuIGlmIGl0J3MgYSBwb2x5ZmlsbCkuXG5cblxuICBpZiAodHlwZSA9PT0gUkVBQ1RfRlJBR01FTlRfVFlQRSB8fCB0eXBlID09PSBSRUFDVF9QUk9GSUxFUl9UWVBFIHx8IGVuYWJsZURlYnVnVHJhY2luZyAgfHwgdHlwZSA9PT0gUkVBQ1RfU1RSSUNUX01PREVfVFlQRSB8fCB0eXBlID09PSBSRUFDVF9TVVNQRU5TRV9UWVBFIHx8IHR5cGUgPT09IFJFQUNUX1NVU1BFTlNFX0xJU1RfVFlQRSB8fCBlbmFibGVMZWdhY3lIaWRkZW4gIHx8IHR5cGUgPT09IFJFQUNUX09GRlNDUkVFTl9UWVBFIHx8IGVuYWJsZVNjb3BlQVBJICB8fCBlbmFibGVDYWNoZUVsZW1lbnQgIHx8IGVuYWJsZVRyYW5zaXRpb25UcmFjaW5nICkge1xuICAgIHJldHVybiB0cnVlO1xuICB9XG5cbiAgaWYgKHR5cGVvZiB0eXBlID09PSAnb2JqZWN0JyAmJiB0eXBlICE9PSBudWxsKSB7XG4gICAgaWYgKHR5cGUuJCR0eXBlb2YgPT09IFJFQUNUX0xBWllfVFlQRSB8fCB0eXBlLiQkdHlwZW9mID09PSBSRUFDVF9NRU1PX1RZUEUgfHwgdHlwZS4kJHR5cGVvZiA9PT0gUkVBQ1RfUFJPVklERVJfVFlQRSB8fCB0eXBlLiQkdHlwZW9mID09PSBSRUFDVF9DT05URVhUX1RZUEUgfHwgdHlwZS4kJHR5cGVvZiA9PT0gUkVBQ1RfRk9SV0FSRF9SRUZfVFlQRSB8fCAvLyBUaGlzIG5lZWRzIHRvIGluY2x1ZGUgYWxsIHBvc3NpYmxlIG1vZHVsZSByZWZlcmVuY2Ugb2JqZWN0XG4gICAgLy8gdHlwZXMgc3VwcG9ydGVkIGJ5IGFueSBGbGlnaHQgY29uZmlndXJhdGlvbiBhbnl3aGVyZSBzaW5jZVxuICAgIC8vIHdlIGRvbid0IGtub3cgd2hpY2ggRmxpZ2h0IGJ1aWxkIHRoaXMgd2lsbCBlbmQgdXAgYmVpbmcgdXNlZFxuICAgIC8vIHdpdGguXG4gICAgdHlwZS4kJHR5cGVvZiA9PT0gUkVBQ1RfQ0xJRU5UX1JFRkVSRU5DRSQxIHx8IHR5cGUuZ2V0TW9kdWxlSWQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIGZhbHNlO1xufVxuXG5mdW5jdGlvbiBnZXRXcmFwcGVkTmFtZShvdXRlclR5cGUsIGlubmVyVHlwZSwgd3JhcHBlck5hbWUpIHtcbiAgdmFyIGRpc3BsYXlOYW1lID0gb3V0ZXJUeXBlLmRpc3BsYXlOYW1lO1xuXG4gIGlmIChkaXNwbGF5TmFtZSkge1xuICAgIHJldHVybiBkaXNwbGF5TmFtZTtcbiAgfVxuXG4gIHZhciBmdW5jdGlvbk5hbWUgPSBpbm5lclR5cGUuZGlzcGxheU5hbWUgfHwgaW5uZXJUeXBlLm5hbWUgfHwgJyc7XG4gIHJldHVybiBmdW5jdGlvbk5hbWUgIT09ICcnID8gd3JhcHBlck5hbWUgKyBcIihcIiArIGZ1bmN0aW9uTmFtZSArIFwiKVwiIDogd3JhcHBlck5hbWU7XG59IC8vIEtlZXAgaW4gc3luYyB3aXRoIHJlYWN0LXJlY29uY2lsZXIvZ2V0Q29tcG9uZW50TmFtZUZyb21GaWJlclxuXG5cbmZ1bmN0aW9uIGdldENvbnRleHROYW1lKHR5cGUpIHtcbiAgcmV0dXJuIHR5cGUuZGlzcGxheU5hbWUgfHwgJ0NvbnRleHQnO1xufSAvLyBOb3RlIHRoYXQgdGhlIHJlY29uY2lsZXIgcGFja2FnZSBzaG91bGQgZ2VuZXJhbGx5IHByZWZlciB0byB1c2UgZ2V0Q29tcG9uZW50TmFtZUZyb21GaWJlcigpIGluc3RlYWQuXG5cblxuZnVuY3Rpb24gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpIHtcbiAgaWYgKHR5cGUgPT0gbnVsbCkge1xuICAgIC8vIEhvc3Qgcm9vdCwgdGV4dCBub2RlIG9yIGp1c3QgaW52YWxpZCB0eXBlLlxuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAge1xuICAgIGlmICh0eXBlb2YgdHlwZS50YWcgPT09ICdudW1iZXInKSB7XG4gICAgICBlcnJvcignUmVjZWl2ZWQgYW4gdW5leHBlY3RlZCBvYmplY3QgaW4gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKCkuICcgKyAnVGhpcyBpcyBsaWtlbHkgYSBidWcgaW4gUmVhY3QuIFBsZWFzZSBmaWxlIGFuIGlzc3VlLicpO1xuICAgIH1cbiAgfVxuXG4gIGlmICh0eXBlb2YgdHlwZSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgIHJldHVybiB0eXBlLmRpc3BsYXlOYW1lIHx8IHR5cGUubmFtZSB8fCBudWxsO1xuICB9XG5cbiAgaWYgKHR5cGVvZiB0eXBlID09PSAnc3RyaW5nJykge1xuICAgIHJldHVybiB0eXBlO1xuICB9XG5cbiAgc3dpdGNoICh0eXBlKSB7XG4gICAgY2FzZSBSRUFDVF9GUkFHTUVOVF9UWVBFOlxuICAgICAgcmV0dXJuICdGcmFnbWVudCc7XG5cbiAgICBjYXNlIFJFQUNUX1BPUlRBTF9UWVBFOlxuICAgICAgcmV0dXJuICdQb3J0YWwnO1xuXG4gICAgY2FzZSBSRUFDVF9QUk9GSUxFUl9UWVBFOlxuICAgICAgcmV0dXJuICdQcm9maWxlcic7XG5cbiAgICBjYXNlIFJFQUNUX1NUUklDVF9NT0RFX1RZUEU6XG4gICAgICByZXR1cm4gJ1N0cmljdE1vZGUnO1xuXG4gICAgY2FzZSBSRUFDVF9TVVNQRU5TRV9UWVBFOlxuICAgICAgcmV0dXJuICdTdXNwZW5zZSc7XG5cbiAgICBjYXNlIFJFQUNUX1NVU1BFTlNFX0xJU1RfVFlQRTpcbiAgICAgIHJldHVybiAnU3VzcGVuc2VMaXN0JztcblxuICAgIGNhc2UgUkVBQ1RfQ0FDSEVfVFlQRTpcbiAgICAgIHtcbiAgICAgICAgcmV0dXJuICdDYWNoZSc7XG4gICAgICB9XG5cbiAgfVxuXG4gIGlmICh0eXBlb2YgdHlwZSA9PT0gJ29iamVjdCcpIHtcbiAgICBzd2l0Y2ggKHR5cGUuJCR0eXBlb2YpIHtcbiAgICAgIGNhc2UgUkVBQ1RfQ09OVEVYVF9UWVBFOlxuICAgICAgICB2YXIgY29udGV4dCA9IHR5cGU7XG4gICAgICAgIHJldHVybiBnZXRDb250ZXh0TmFtZShjb250ZXh0KSArICcuQ29uc3VtZXInO1xuXG4gICAgICBjYXNlIFJFQUNUX1BST1ZJREVSX1RZUEU6XG4gICAgICAgIHZhciBwcm92aWRlciA9IHR5cGU7XG4gICAgICAgIHJldHVybiBnZXRDb250ZXh0TmFtZShwcm92aWRlci5fY29udGV4dCkgKyAnLlByb3ZpZGVyJztcblxuICAgICAgY2FzZSBSRUFDVF9GT1JXQVJEX1JFRl9UWVBFOlxuICAgICAgICByZXR1cm4gZ2V0V3JhcHBlZE5hbWUodHlwZSwgdHlwZS5yZW5kZXIsICdGb3J3YXJkUmVmJyk7XG5cbiAgICAgIGNhc2UgUkVBQ1RfTUVNT19UWVBFOlxuICAgICAgICB2YXIgb3V0ZXJOYW1lID0gdHlwZS5kaXNwbGF5TmFtZSB8fCBudWxsO1xuXG4gICAgICAgIGlmIChvdXRlck5hbWUgIT09IG51bGwpIHtcbiAgICAgICAgICByZXR1cm4gb3V0ZXJOYW1lO1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlLnR5cGUpIHx8ICdNZW1vJztcblxuICAgICAgY2FzZSBSRUFDVF9MQVpZX1RZUEU6XG4gICAgICAgIHtcbiAgICAgICAgICB2YXIgbGF6eUNvbXBvbmVudCA9IHR5cGU7XG4gICAgICAgICAgdmFyIHBheWxvYWQgPSBsYXp5Q29tcG9uZW50Ll9wYXlsb2FkO1xuICAgICAgICAgIHZhciBpbml0ID0gbGF6eUNvbXBvbmVudC5faW5pdDtcblxuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICByZXR1cm4gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKGluaXQocGF5bG9hZCkpO1xuICAgICAgICAgIH0gY2F0Y2ggKHgpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIG51bGw7XG59XG5cbnZhciBhc3NpZ24gPSBPYmplY3QuYXNzaWduO1xuXG4vLyBIZWxwZXJzIHRvIHBhdGNoIGNvbnNvbGUubG9ncyB0byBhdm9pZCBsb2dnaW5nIGR1cmluZyBzaWRlLWVmZmVjdCBmcmVlXG4vLyByZXBsYXlpbmcgb24gcmVuZGVyIGZ1bmN0aW9uLiBUaGlzIGN1cnJlbnRseSBvbmx5IHBhdGNoZXMgdGhlIG9iamVjdFxuLy8gbGF6aWx5IHdoaWNoIHdvbid0IGNvdmVyIGlmIHRoZSBsb2cgZnVuY3Rpb24gd2FzIGV4dHJhY3RlZCBlYWdlcmx5LlxuLy8gV2UgY291bGQgYWxzbyBlYWdlcmx5IHBhdGNoIHRoZSBtZXRob2QuXG52YXIgZGlzYWJsZWREZXB0aCA9IDA7XG52YXIgcHJldkxvZztcbnZhciBwcmV2SW5mbztcbnZhciBwcmV2V2FybjtcbnZhciBwcmV2RXJyb3I7XG52YXIgcHJldkdyb3VwO1xudmFyIHByZXZHcm91cENvbGxhcHNlZDtcbnZhciBwcmV2R3JvdXBFbmQ7XG5cbmZ1bmN0aW9uIGRpc2FibGVkTG9nKCkge31cblxuZGlzYWJsZWRMb2cuX19yZWFjdERpc2FibGVkTG9nID0gdHJ1ZTtcbmZ1bmN0aW9uIGRpc2FibGVMb2dzKCkge1xuICB7XG4gICAgaWYgKGRpc2FibGVkRGVwdGggPT09IDApIHtcbiAgICAgIC8qIGVzbGludC1kaXNhYmxlIHJlYWN0LWludGVybmFsL25vLXByb2R1Y3Rpb24tbG9nZ2luZyAqL1xuICAgICAgcHJldkxvZyA9IGNvbnNvbGUubG9nO1xuICAgICAgcHJldkluZm8gPSBjb25zb2xlLmluZm87XG4gICAgICBwcmV2V2FybiA9IGNvbnNvbGUud2FybjtcbiAgICAgIHByZXZFcnJvciA9IGNvbnNvbGUuZXJyb3I7XG4gICAgICBwcmV2R3JvdXAgPSBjb25zb2xlLmdyb3VwO1xuICAgICAgcHJldkdyb3VwQ29sbGFwc2VkID0gY29uc29sZS5ncm91cENvbGxhcHNlZDtcbiAgICAgIHByZXZHcm91cEVuZCA9IGNvbnNvbGUuZ3JvdXBFbmQ7IC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9mYWNlYm9vay9yZWFjdC9pc3N1ZXMvMTkwOTlcblxuICAgICAgdmFyIHByb3BzID0ge1xuICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgIHZhbHVlOiBkaXNhYmxlZExvZyxcbiAgICAgICAgd3JpdGFibGU6IHRydWVcbiAgICAgIH07IC8vICRGbG93Rml4TWVbY2Fubm90LXdyaXRlXSBGbG93IHRoaW5rcyBjb25zb2xlIGlzIGltbXV0YWJsZS5cblxuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnRpZXMoY29uc29sZSwge1xuICAgICAgICBpbmZvOiBwcm9wcyxcbiAgICAgICAgbG9nOiBwcm9wcyxcbiAgICAgICAgd2FybjogcHJvcHMsXG4gICAgICAgIGVycm9yOiBwcm9wcyxcbiAgICAgICAgZ3JvdXA6IHByb3BzLFxuICAgICAgICBncm91cENvbGxhcHNlZDogcHJvcHMsXG4gICAgICAgIGdyb3VwRW5kOiBwcm9wc1xuICAgICAgfSk7XG4gICAgICAvKiBlc2xpbnQtZW5hYmxlIHJlYWN0LWludGVybmFsL25vLXByb2R1Y3Rpb24tbG9nZ2luZyAqL1xuICAgIH1cblxuICAgIGRpc2FibGVkRGVwdGgrKztcbiAgfVxufVxuZnVuY3Rpb24gcmVlbmFibGVMb2dzKCkge1xuICB7XG4gICAgZGlzYWJsZWREZXB0aC0tO1xuXG4gICAgaWYgKGRpc2FibGVkRGVwdGggPT09IDApIHtcbiAgICAgIC8qIGVzbGludC1kaXNhYmxlIHJlYWN0LWludGVybmFsL25vLXByb2R1Y3Rpb24tbG9nZ2luZyAqL1xuICAgICAgdmFyIHByb3BzID0ge1xuICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgIHdyaXRhYmxlOiB0cnVlXG4gICAgICB9OyAvLyAkRmxvd0ZpeE1lW2Nhbm5vdC13cml0ZV0gRmxvdyB0aGlua3MgY29uc29sZSBpcyBpbW11dGFibGUuXG5cbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0aWVzKGNvbnNvbGUsIHtcbiAgICAgICAgbG9nOiBhc3NpZ24oe30sIHByb3BzLCB7XG4gICAgICAgICAgdmFsdWU6IHByZXZMb2dcbiAgICAgICAgfSksXG4gICAgICAgIGluZm86IGFzc2lnbih7fSwgcHJvcHMsIHtcbiAgICAgICAgICB2YWx1ZTogcHJldkluZm9cbiAgICAgICAgfSksXG4gICAgICAgIHdhcm46IGFzc2lnbih7fSwgcHJvcHMsIHtcbiAgICAgICAgICB2YWx1ZTogcHJldldhcm5cbiAgICAgICAgfSksXG4gICAgICAgIGVycm9yOiBhc3NpZ24oe30sIHByb3BzLCB7XG4gICAgICAgICAgdmFsdWU6IHByZXZFcnJvclxuICAgICAgICB9KSxcbiAgICAgICAgZ3JvdXA6IGFzc2lnbih7fSwgcHJvcHMsIHtcbiAgICAgICAgICB2YWx1ZTogcHJldkdyb3VwXG4gICAgICAgIH0pLFxuICAgICAgICBncm91cENvbGxhcHNlZDogYXNzaWduKHt9LCBwcm9wcywge1xuICAgICAgICAgIHZhbHVlOiBwcmV2R3JvdXBDb2xsYXBzZWRcbiAgICAgICAgfSksXG4gICAgICAgIGdyb3VwRW5kOiBhc3NpZ24oe30sIHByb3BzLCB7XG4gICAgICAgICAgdmFsdWU6IHByZXZHcm91cEVuZFxuICAgICAgICB9KVxuICAgICAgfSk7XG4gICAgICAvKiBlc2xpbnQtZW5hYmxlIHJlYWN0LWludGVybmFsL25vLXByb2R1Y3Rpb24tbG9nZ2luZyAqL1xuICAgIH1cblxuICAgIGlmIChkaXNhYmxlZERlcHRoIDwgMCkge1xuICAgICAgZXJyb3IoJ2Rpc2FibGVkRGVwdGggZmVsbCBiZWxvdyB6ZXJvLiAnICsgJ1RoaXMgaXMgYSBidWcgaW4gUmVhY3QuIFBsZWFzZSBmaWxlIGFuIGlzc3VlLicpO1xuICAgIH1cbiAgfVxufVxuXG52YXIgUmVhY3RDdXJyZW50RGlzcGF0Y2hlciA9IFJlYWN0U2hhcmVkSW50ZXJuYWxzLlJlYWN0Q3VycmVudERpc3BhdGNoZXI7XG52YXIgcHJlZml4O1xuZnVuY3Rpb24gZGVzY3JpYmVCdWlsdEluQ29tcG9uZW50RnJhbWUobmFtZSwgc291cmNlLCBvd25lckZuKSB7XG4gIHtcbiAgICBpZiAocHJlZml4ID09PSB1bmRlZmluZWQpIHtcbiAgICAgIC8vIEV4dHJhY3QgdGhlIFZNIHNwZWNpZmljIHByZWZpeCB1c2VkIGJ5IGVhY2ggbGluZS5cbiAgICAgIHRyeSB7XG4gICAgICAgIHRocm93IEVycm9yKCk7XG4gICAgICB9IGNhdGNoICh4KSB7XG4gICAgICAgIHZhciBtYXRjaCA9IHguc3RhY2sudHJpbSgpLm1hdGNoKC9cXG4oICooYXQgKT8pLyk7XG4gICAgICAgIHByZWZpeCA9IG1hdGNoICYmIG1hdGNoWzFdIHx8ICcnO1xuICAgICAgfVxuICAgIH0gLy8gV2UgdXNlIHRoZSBwcmVmaXggdG8gZW5zdXJlIG91ciBzdGFja3MgbGluZSB1cCB3aXRoIG5hdGl2ZSBzdGFjayBmcmFtZXMuXG5cblxuICAgIHJldHVybiAnXFxuJyArIHByZWZpeCArIG5hbWU7XG4gIH1cbn1cbnZhciByZWVudHJ5ID0gZmFsc2U7XG52YXIgY29tcG9uZW50RnJhbWVDYWNoZTtcblxue1xuICB2YXIgUG9zc2libHlXZWFrTWFwID0gdHlwZW9mIFdlYWtNYXAgPT09ICdmdW5jdGlvbicgPyBXZWFrTWFwIDogTWFwO1xuICBjb21wb25lbnRGcmFtZUNhY2hlID0gbmV3IFBvc3NpYmx5V2Vha01hcCgpO1xufVxuXG5mdW5jdGlvbiBkZXNjcmliZU5hdGl2ZUNvbXBvbmVudEZyYW1lKGZuLCBjb25zdHJ1Y3QpIHtcbiAgLy8gSWYgc29tZXRoaW5nIGFza2VkIGZvciBhIHN0YWNrIGluc2lkZSBhIGZha2UgcmVuZGVyLCBpdCBzaG91bGQgZ2V0IGlnbm9yZWQuXG4gIGlmICghZm4gfHwgcmVlbnRyeSkge1xuICAgIHJldHVybiAnJztcbiAgfVxuXG4gIHtcbiAgICB2YXIgZnJhbWUgPSBjb21wb25lbnRGcmFtZUNhY2hlLmdldChmbik7XG5cbiAgICBpZiAoZnJhbWUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgcmV0dXJuIGZyYW1lO1xuICAgIH1cbiAgfVxuXG4gIHZhciBjb250cm9sO1xuICByZWVudHJ5ID0gdHJ1ZTtcbiAgdmFyIHByZXZpb3VzUHJlcGFyZVN0YWNrVHJhY2UgPSBFcnJvci5wcmVwYXJlU3RhY2tUcmFjZTsgLy8gJEZsb3dGaXhNZVtpbmNvbXBhdGlibGUtdHlwZV0gSXQgZG9lcyBhY2NlcHQgdW5kZWZpbmVkLlxuXG4gIEVycm9yLnByZXBhcmVTdGFja1RyYWNlID0gdW5kZWZpbmVkO1xuICB2YXIgcHJldmlvdXNEaXNwYXRjaGVyO1xuXG4gIHtcbiAgICBwcmV2aW91c0Rpc3BhdGNoZXIgPSBSZWFjdEN1cnJlbnREaXNwYXRjaGVyLmN1cnJlbnQ7IC8vIFNldCB0aGUgZGlzcGF0Y2hlciBpbiBERVYgYmVjYXVzZSB0aGlzIG1pZ2h0IGJlIGNhbGwgaW4gdGhlIHJlbmRlciBmdW5jdGlvblxuICAgIC8vIGZvciB3YXJuaW5ncy5cblxuICAgIFJlYWN0Q3VycmVudERpc3BhdGNoZXIuY3VycmVudCA9IG51bGw7XG4gICAgZGlzYWJsZUxvZ3MoKTtcbiAgfVxuXG4gIHRyeSB7XG4gICAgLy8gVGhpcyBzaG91bGQgdGhyb3cuXG4gICAgaWYgKGNvbnN0cnVjdCkge1xuICAgICAgLy8gU29tZXRoaW5nIHNob3VsZCBiZSBzZXR0aW5nIHRoZSBwcm9wcyBpbiB0aGUgY29uc3RydWN0b3IuXG4gICAgICB2YXIgRmFrZSA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdGhyb3cgRXJyb3IoKTtcbiAgICAgIH07IC8vICRGbG93Rml4TWVbcHJvcC1taXNzaW5nXVxuXG5cbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShGYWtlLnByb3RvdHlwZSwgJ3Byb3BzJywge1xuICAgICAgICBzZXQ6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAvLyBXZSB1c2UgYSB0aHJvd2luZyBzZXR0ZXIgaW5zdGVhZCBvZiBmcm96ZW4gb3Igbm9uLXdyaXRhYmxlIHByb3BzXG4gICAgICAgICAgLy8gYmVjYXVzZSB0aGF0IHdvbid0IHRocm93IGluIGEgbm9uLXN0cmljdCBtb2RlIGZ1bmN0aW9uLlxuICAgICAgICAgIHRocm93IEVycm9yKCk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICBpZiAodHlwZW9mIFJlZmxlY3QgPT09ICdvYmplY3QnICYmIFJlZmxlY3QuY29uc3RydWN0KSB7XG4gICAgICAgIC8vIFdlIGNvbnN0cnVjdCBhIGRpZmZlcmVudCBjb250cm9sIGZvciB0aGlzIGNhc2UgdG8gaW5jbHVkZSBhbnkgZXh0cmFcbiAgICAgICAgLy8gZnJhbWVzIGFkZGVkIGJ5IHRoZSBjb25zdHJ1Y3QgY2FsbC5cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBSZWZsZWN0LmNvbnN0cnVjdChGYWtlLCBbXSk7XG4gICAgICAgIH0gY2F0Y2ggKHgpIHtcbiAgICAgICAgICBjb250cm9sID0geDtcbiAgICAgICAgfVxuXG4gICAgICAgIFJlZmxlY3QuY29uc3RydWN0KGZuLCBbXSwgRmFrZSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIEZha2UuY2FsbCgpO1xuICAgICAgICB9IGNhdGNoICh4KSB7XG4gICAgICAgICAgY29udHJvbCA9IHg7XG4gICAgICAgIH0gLy8gJEZsb3dGaXhNZVtwcm9wLW1pc3NpbmddIGZvdW5kIHdoZW4gdXBncmFkaW5nIEZsb3dcblxuXG4gICAgICAgIGZuLmNhbGwoRmFrZS5wcm90b3R5cGUpO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICB0cnkge1xuICAgICAgICB0aHJvdyBFcnJvcigpO1xuICAgICAgfSBjYXRjaCAoeCkge1xuICAgICAgICBjb250cm9sID0geDtcbiAgICAgIH0gLy8gVE9ETyhsdW5hKTogVGhpcyB3aWxsIGN1cnJlbnRseSBvbmx5IHRocm93IGlmIHRoZSBmdW5jdGlvbiBjb21wb25lbnRcbiAgICAgIC8vIHRyaWVzIHRvIGFjY2VzcyBSZWFjdC9SZWFjdERPTS9wcm9wcy4gV2Ugc2hvdWxkIHByb2JhYmx5IG1ha2UgdGhpcyB0aHJvd1xuICAgICAgLy8gaW4gc2ltcGxlIGNvbXBvbmVudHMgdG9vXG5cblxuICAgICAgdmFyIG1heWJlUHJvbWlzZSA9IGZuKCk7IC8vIElmIHRoZSBmdW5jdGlvbiBjb21wb25lbnQgcmV0dXJucyBhIHByb21pc2UsIGl0J3MgbGlrZWx5IGFuIGFzeW5jXG4gICAgICAvLyBjb21wb25lbnQsIHdoaWNoIHdlIGRvbid0IHlldCBzdXBwb3J0LiBBdHRhY2ggYSBub29wIGNhdGNoIGhhbmRsZXIgdG9cbiAgICAgIC8vIHNpbGVuY2UgdGhlIGVycm9yLlxuICAgICAgLy8gVE9ETzogSW1wbGVtZW50IGNvbXBvbmVudCBzdGFja3MgZm9yIGFzeW5jIGNsaWVudCBjb21wb25lbnRzP1xuXG4gICAgICBpZiAobWF5YmVQcm9taXNlICYmIHR5cGVvZiBtYXliZVByb21pc2UuY2F0Y2ggPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgbWF5YmVQcm9taXNlLmNhdGNoKGZ1bmN0aW9uICgpIHt9KTtcbiAgICAgIH1cbiAgICB9XG4gIH0gY2F0Y2ggKHNhbXBsZSkge1xuICAgIC8vIFRoaXMgaXMgaW5saW5lZCBtYW51YWxseSBiZWNhdXNlIGNsb3N1cmUgZG9lc24ndCBkbyBpdCBmb3IgdXMuXG4gICAgaWYgKHNhbXBsZSAmJiBjb250cm9sICYmIHR5cGVvZiBzYW1wbGUuc3RhY2sgPT09ICdzdHJpbmcnKSB7XG4gICAgICAvLyBUaGlzIGV4dHJhY3RzIHRoZSBmaXJzdCBmcmFtZSBmcm9tIHRoZSBzYW1wbGUgdGhhdCBpc24ndCBhbHNvIGluIHRoZSBjb250cm9sLlxuICAgICAgLy8gU2tpcHBpbmcgb25lIGZyYW1lIHRoYXQgd2UgYXNzdW1lIGlzIHRoZSBmcmFtZSB0aGF0IGNhbGxzIHRoZSB0d28uXG4gICAgICB2YXIgc2FtcGxlTGluZXMgPSBzYW1wbGUuc3RhY2suc3BsaXQoJ1xcbicpO1xuICAgICAgdmFyIGNvbnRyb2xMaW5lcyA9IGNvbnRyb2wuc3RhY2suc3BsaXQoJ1xcbicpO1xuICAgICAgdmFyIHMgPSBzYW1wbGVMaW5lcy5sZW5ndGggLSAxO1xuICAgICAgdmFyIGMgPSBjb250cm9sTGluZXMubGVuZ3RoIC0gMTtcblxuICAgICAgd2hpbGUgKHMgPj0gMSAmJiBjID49IDAgJiYgc2FtcGxlTGluZXNbc10gIT09IGNvbnRyb2xMaW5lc1tjXSkge1xuICAgICAgICAvLyBXZSBleHBlY3QgYXQgbGVhc3Qgb25lIHN0YWNrIGZyYW1lIHRvIGJlIHNoYXJlZC5cbiAgICAgICAgLy8gVHlwaWNhbGx5IHRoaXMgd2lsbCBiZSB0aGUgcm9vdCBtb3N0IG9uZS4gSG93ZXZlciwgc3RhY2sgZnJhbWVzIG1heSBiZVxuICAgICAgICAvLyBjdXQgb2ZmIGR1ZSB0byBtYXhpbXVtIHN0YWNrIGxpbWl0cy4gSW4gdGhpcyBjYXNlLCBvbmUgbWF5YmUgY3V0IG9mZlxuICAgICAgICAvLyBlYXJsaWVyIHRoYW4gdGhlIG90aGVyLiBXZSBhc3N1bWUgdGhhdCB0aGUgc2FtcGxlIGlzIGxvbmdlciBvciB0aGUgc2FtZVxuICAgICAgICAvLyBhbmQgdGhlcmUgZm9yIGN1dCBvZmYgZWFybGllci4gU28gd2Ugc2hvdWxkIGZpbmQgdGhlIHJvb3QgbW9zdCBmcmFtZSBpblxuICAgICAgICAvLyB0aGUgc2FtcGxlIHNvbWV3aGVyZSBpbiB0aGUgY29udHJvbC5cbiAgICAgICAgYy0tO1xuICAgICAgfVxuXG4gICAgICBmb3IgKDsgcyA+PSAxICYmIGMgPj0gMDsgcy0tLCBjLS0pIHtcbiAgICAgICAgLy8gTmV4dCB3ZSBmaW5kIHRoZSBmaXJzdCBvbmUgdGhhdCBpc24ndCB0aGUgc2FtZSB3aGljaCBzaG91bGQgYmUgdGhlXG4gICAgICAgIC8vIGZyYW1lIHRoYXQgY2FsbGVkIG91ciBzYW1wbGUgZnVuY3Rpb24gYW5kIHRoZSBjb250cm9sLlxuICAgICAgICBpZiAoc2FtcGxlTGluZXNbc10gIT09IGNvbnRyb2xMaW5lc1tjXSkge1xuICAgICAgICAgIC8vIEluIFY4LCB0aGUgZmlyc3QgbGluZSBpcyBkZXNjcmliaW5nIHRoZSBtZXNzYWdlIGJ1dCBvdGhlciBWTXMgZG9uJ3QuXG4gICAgICAgICAgLy8gSWYgd2UncmUgYWJvdXQgdG8gcmV0dXJuIHRoZSBmaXJzdCBsaW5lLCBhbmQgdGhlIGNvbnRyb2wgaXMgYWxzbyBvbiB0aGUgc2FtZVxuICAgICAgICAgIC8vIGxpbmUsIHRoYXQncyBhIHByZXR0eSBnb29kIGluZGljYXRvciB0aGF0IG91ciBzYW1wbGUgdGhyZXcgYXQgc2FtZSBsaW5lIGFzXG4gICAgICAgICAgLy8gdGhlIGNvbnRyb2wuIEkuZS4gYmVmb3JlIHdlIGVudGVyZWQgdGhlIHNhbXBsZSBmcmFtZS4gU28gd2UgaWdub3JlIHRoaXMgcmVzdWx0LlxuICAgICAgICAgIC8vIFRoaXMgY2FuIGhhcHBlbiBpZiB5b3UgcGFzc2VkIGEgY2xhc3MgdG8gZnVuY3Rpb24gY29tcG9uZW50LCBvciBub24tZnVuY3Rpb24uXG4gICAgICAgICAgaWYgKHMgIT09IDEgfHwgYyAhPT0gMSkge1xuICAgICAgICAgICAgZG8ge1xuICAgICAgICAgICAgICBzLS07XG4gICAgICAgICAgICAgIGMtLTsgLy8gV2UgbWF5IHN0aWxsIGhhdmUgc2ltaWxhciBpbnRlcm1lZGlhdGUgZnJhbWVzIGZyb20gdGhlIGNvbnN0cnVjdCBjYWxsLlxuICAgICAgICAgICAgICAvLyBUaGUgbmV4dCBvbmUgdGhhdCBpc24ndCB0aGUgc2FtZSBzaG91bGQgYmUgb3VyIG1hdGNoIHRob3VnaC5cblxuICAgICAgICAgICAgICBpZiAoYyA8IDAgfHwgc2FtcGxlTGluZXNbc10gIT09IGNvbnRyb2xMaW5lc1tjXSkge1xuICAgICAgICAgICAgICAgIC8vIFY4IGFkZHMgYSBcIm5ld1wiIHByZWZpeCBmb3IgbmF0aXZlIGNsYXNzZXMuIExldCdzIHJlbW92ZSBpdCB0byBtYWtlIGl0IHByZXR0aWVyLlxuICAgICAgICAgICAgICAgIHZhciBfZnJhbWUgPSAnXFxuJyArIHNhbXBsZUxpbmVzW3NdLnJlcGxhY2UoJyBhdCBuZXcgJywgJyBhdCAnKTsgLy8gSWYgb3VyIGNvbXBvbmVudCBmcmFtZSBpcyBsYWJlbGVkIFwiPGFub255bW91cz5cIlxuICAgICAgICAgICAgICAgIC8vIGJ1dCB3ZSBoYXZlIGEgdXNlci1wcm92aWRlZCBcImRpc3BsYXlOYW1lXCJcbiAgICAgICAgICAgICAgICAvLyBzcGxpY2UgaXQgaW4gdG8gbWFrZSB0aGUgc3RhY2sgbW9yZSByZWFkYWJsZS5cblxuXG4gICAgICAgICAgICAgICAgaWYgKGZuLmRpc3BsYXlOYW1lICYmIF9mcmFtZS5pbmNsdWRlcygnPGFub255bW91cz4nKSkge1xuICAgICAgICAgICAgICAgICAgX2ZyYW1lID0gX2ZyYW1lLnJlcGxhY2UoJzxhbm9ueW1vdXM+JywgZm4uZGlzcGxheU5hbWUpO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgZm4gPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgICAgICAgICAgY29tcG9uZW50RnJhbWVDYWNoZS5zZXQoZm4sIF9mcmFtZSk7XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSAvLyBSZXR1cm4gdGhlIGxpbmUgd2UgZm91bmQuXG5cblxuICAgICAgICAgICAgICAgIHJldHVybiBfZnJhbWU7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gd2hpbGUgKHMgPj0gMSAmJiBjID49IDApO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9IGZpbmFsbHkge1xuICAgIHJlZW50cnkgPSBmYWxzZTtcblxuICAgIHtcbiAgICAgIFJlYWN0Q3VycmVudERpc3BhdGNoZXIuY3VycmVudCA9IHByZXZpb3VzRGlzcGF0Y2hlcjtcbiAgICAgIHJlZW5hYmxlTG9ncygpO1xuICAgIH1cblxuICAgIEVycm9yLnByZXBhcmVTdGFja1RyYWNlID0gcHJldmlvdXNQcmVwYXJlU3RhY2tUcmFjZTtcbiAgfSAvLyBGYWxsYmFjayB0byBqdXN0IHVzaW5nIHRoZSBuYW1lIGlmIHdlIGNvdWxkbid0IG1ha2UgaXQgdGhyb3cuXG5cblxuICB2YXIgbmFtZSA9IGZuID8gZm4uZGlzcGxheU5hbWUgfHwgZm4ubmFtZSA6ICcnO1xuICB2YXIgc3ludGhldGljRnJhbWUgPSBuYW1lID8gZGVzY3JpYmVCdWlsdEluQ29tcG9uZW50RnJhbWUobmFtZSkgOiAnJztcblxuICB7XG4gICAgaWYgKHR5cGVvZiBmbiA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgY29tcG9uZW50RnJhbWVDYWNoZS5zZXQoZm4sIHN5bnRoZXRpY0ZyYW1lKTtcbiAgICB9XG4gIH1cblxuICByZXR1cm4gc3ludGhldGljRnJhbWU7XG59XG5mdW5jdGlvbiBkZXNjcmliZUZ1bmN0aW9uQ29tcG9uZW50RnJhbWUoZm4sIHNvdXJjZSwgb3duZXJGbikge1xuICB7XG4gICAgcmV0dXJuIGRlc2NyaWJlTmF0aXZlQ29tcG9uZW50RnJhbWUoZm4sIGZhbHNlKTtcbiAgfVxufVxuXG5mdW5jdGlvbiBzaG91bGRDb25zdHJ1Y3QoQ29tcG9uZW50KSB7XG4gIHZhciBwcm90b3R5cGUgPSBDb21wb25lbnQucHJvdG90eXBlO1xuICByZXR1cm4gISEocHJvdG90eXBlICYmIHByb3RvdHlwZS5pc1JlYWN0Q29tcG9uZW50KTtcbn1cblxuZnVuY3Rpb24gZGVzY3JpYmVVbmtub3duRWxlbWVudFR5cGVGcmFtZUluREVWKHR5cGUsIHNvdXJjZSwgb3duZXJGbikge1xuXG4gIGlmICh0eXBlID09IG51bGwpIHtcbiAgICByZXR1cm4gJyc7XG4gIH1cblxuICBpZiAodHlwZW9mIHR5cGUgPT09ICdmdW5jdGlvbicpIHtcbiAgICB7XG4gICAgICByZXR1cm4gZGVzY3JpYmVOYXRpdmVDb21wb25lbnRGcmFtZSh0eXBlLCBzaG91bGRDb25zdHJ1Y3QodHlwZSkpO1xuICAgIH1cbiAgfVxuXG4gIGlmICh0eXBlb2YgdHlwZSA9PT0gJ3N0cmluZycpIHtcbiAgICByZXR1cm4gZGVzY3JpYmVCdWlsdEluQ29tcG9uZW50RnJhbWUodHlwZSk7XG4gIH1cblxuICBzd2l0Y2ggKHR5cGUpIHtcbiAgICBjYXNlIFJFQUNUX1NVU1BFTlNFX1RZUEU6XG4gICAgICByZXR1cm4gZGVzY3JpYmVCdWlsdEluQ29tcG9uZW50RnJhbWUoJ1N1c3BlbnNlJyk7XG5cbiAgICBjYXNlIFJFQUNUX1NVU1BFTlNFX0xJU1RfVFlQRTpcbiAgICAgIHJldHVybiBkZXNjcmliZUJ1aWx0SW5Db21wb25lbnRGcmFtZSgnU3VzcGVuc2VMaXN0Jyk7XG4gIH1cblxuICBpZiAodHlwZW9mIHR5cGUgPT09ICdvYmplY3QnKSB7XG4gICAgc3dpdGNoICh0eXBlLiQkdHlwZW9mKSB7XG4gICAgICBjYXNlIFJFQUNUX0ZPUldBUkRfUkVGX1RZUEU6XG4gICAgICAgIHJldHVybiBkZXNjcmliZUZ1bmN0aW9uQ29tcG9uZW50RnJhbWUodHlwZS5yZW5kZXIpO1xuXG4gICAgICBjYXNlIFJFQUNUX01FTU9fVFlQRTpcbiAgICAgICAgLy8gTWVtbyBtYXkgY29udGFpbiBhbnkgY29tcG9uZW50IHR5cGUgc28gd2UgcmVjdXJzaXZlbHkgcmVzb2x2ZSBpdC5cbiAgICAgICAgcmV0dXJuIGRlc2NyaWJlVW5rbm93bkVsZW1lbnRUeXBlRnJhbWVJbkRFVih0eXBlLnR5cGUsIHNvdXJjZSwgb3duZXJGbik7XG5cbiAgICAgIGNhc2UgUkVBQ1RfTEFaWV9UWVBFOlxuICAgICAgICB7XG4gICAgICAgICAgdmFyIGxhenlDb21wb25lbnQgPSB0eXBlO1xuICAgICAgICAgIHZhciBwYXlsb2FkID0gbGF6eUNvbXBvbmVudC5fcGF5bG9hZDtcbiAgICAgICAgICB2YXIgaW5pdCA9IGxhenlDb21wb25lbnQuX2luaXQ7XG5cbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gTGF6eSBtYXkgY29udGFpbiBhbnkgY29tcG9uZW50IHR5cGUgc28gd2UgcmVjdXJzaXZlbHkgcmVzb2x2ZSBpdC5cbiAgICAgICAgICAgIHJldHVybiBkZXNjcmliZVVua25vd25FbGVtZW50VHlwZUZyYW1lSW5ERVYoaW5pdChwYXlsb2FkKSwgc291cmNlLCBvd25lckZuKTtcbiAgICAgICAgICB9IGNhdGNoICh4KSB7fVxuICAgICAgICB9XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuICcnO1xufVxuXG4vLyAkRmxvd0ZpeE1lW21ldGhvZC11bmJpbmRpbmddXG52YXIgaGFzT3duUHJvcGVydHkgPSBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5O1xuXG52YXIgbG9nZ2VkVHlwZUZhaWx1cmVzID0ge307XG52YXIgUmVhY3REZWJ1Z0N1cnJlbnRGcmFtZSQxID0gUmVhY3RTaGFyZWRJbnRlcm5hbHMuUmVhY3REZWJ1Z0N1cnJlbnRGcmFtZTtcblxuZnVuY3Rpb24gc2V0Q3VycmVudGx5VmFsaWRhdGluZ0VsZW1lbnQkMShlbGVtZW50KSB7XG4gIHtcbiAgICBpZiAoZWxlbWVudCkge1xuICAgICAgdmFyIG93bmVyID0gZWxlbWVudC5fb3duZXI7XG4gICAgICB2YXIgc3RhY2sgPSBkZXNjcmliZVVua25vd25FbGVtZW50VHlwZUZyYW1lSW5ERVYoZWxlbWVudC50eXBlLCBlbGVtZW50Ll9zb3VyY2UsIG93bmVyID8gb3duZXIudHlwZSA6IG51bGwpO1xuICAgICAgUmVhY3REZWJ1Z0N1cnJlbnRGcmFtZSQxLnNldEV4dHJhU3RhY2tGcmFtZShzdGFjayk7XG4gICAgfSBlbHNlIHtcbiAgICAgIFJlYWN0RGVidWdDdXJyZW50RnJhbWUkMS5zZXRFeHRyYVN0YWNrRnJhbWUobnVsbCk7XG4gICAgfVxuICB9XG59XG5cbmZ1bmN0aW9uIGNoZWNrUHJvcFR5cGVzKHR5cGVTcGVjcywgdmFsdWVzLCBsb2NhdGlvbiwgY29tcG9uZW50TmFtZSwgZWxlbWVudCkge1xuICB7XG4gICAgLy8gJEZsb3dGaXhNZVtpbmNvbXBhdGlibGUtdXNlXSBUaGlzIGlzIG9rYXkgYnV0IEZsb3cgZG9lc24ndCBrbm93IGl0LlxuICAgIHZhciBoYXMgPSBGdW5jdGlvbi5jYWxsLmJpbmQoaGFzT3duUHJvcGVydHkpO1xuXG4gICAgZm9yICh2YXIgdHlwZVNwZWNOYW1lIGluIHR5cGVTcGVjcykge1xuICAgICAgaWYgKGhhcyh0eXBlU3BlY3MsIHR5cGVTcGVjTmFtZSkpIHtcbiAgICAgICAgdmFyIGVycm9yJDEgPSB2b2lkIDA7IC8vIFByb3AgdHlwZSB2YWxpZGF0aW9uIG1heSB0aHJvdy4gSW4gY2FzZSB0aGV5IGRvLCB3ZSBkb24ndCB3YW50IHRvXG4gICAgICAgIC8vIGZhaWwgdGhlIHJlbmRlciBwaGFzZSB3aGVyZSBpdCBkaWRuJ3QgZmFpbCBiZWZvcmUuIFNvIHdlIGxvZyBpdC5cbiAgICAgICAgLy8gQWZ0ZXIgdGhlc2UgaGF2ZSBiZWVuIGNsZWFuZWQgdXAsIHdlJ2xsIGxldCB0aGVtIHRocm93LlxuXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgLy8gVGhpcyBpcyBpbnRlbnRpb25hbGx5IGFuIGludmFyaWFudCB0aGF0IGdldHMgY2F1Z2h0LiBJdCdzIHRoZSBzYW1lXG4gICAgICAgICAgLy8gYmVoYXZpb3IgYXMgd2l0aG91dCB0aGlzIHN0YXRlbWVudCBleGNlcHQgd2l0aCBhIGJldHRlciBtZXNzYWdlLlxuICAgICAgICAgIGlmICh0eXBlb2YgdHlwZVNwZWNzW3R5cGVTcGVjTmFtZV0gIT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC1pbnRlcm5hbC9wcm9kLWVycm9yLWNvZGVzXG4gICAgICAgICAgICB2YXIgZXJyID0gRXJyb3IoKGNvbXBvbmVudE5hbWUgfHwgJ1JlYWN0IGNsYXNzJykgKyAnOiAnICsgbG9jYXRpb24gKyAnIHR5cGUgYCcgKyB0eXBlU3BlY05hbWUgKyAnYCBpcyBpbnZhbGlkOyAnICsgJ2l0IG11c3QgYmUgYSBmdW5jdGlvbiwgdXN1YWxseSBmcm9tIHRoZSBgcHJvcC10eXBlc2AgcGFja2FnZSwgYnV0IHJlY2VpdmVkIGAnICsgdHlwZW9mIHR5cGVTcGVjc1t0eXBlU3BlY05hbWVdICsgJ2AuJyArICdUaGlzIG9mdGVuIGhhcHBlbnMgYmVjYXVzZSBvZiB0eXBvcyBzdWNoIGFzIGBQcm9wVHlwZXMuZnVuY3Rpb25gIGluc3RlYWQgb2YgYFByb3BUeXBlcy5mdW5jYC4nKTtcbiAgICAgICAgICAgIGVyci5uYW1lID0gJ0ludmFyaWFudCBWaW9sYXRpb24nO1xuICAgICAgICAgICAgdGhyb3cgZXJyO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGVycm9yJDEgPSB0eXBlU3BlY3NbdHlwZVNwZWNOYW1lXSh2YWx1ZXMsIHR5cGVTcGVjTmFtZSwgY29tcG9uZW50TmFtZSwgbG9jYXRpb24sIG51bGwsICdTRUNSRVRfRE9fTk9UX1BBU1NfVEhJU19PUl9ZT1VfV0lMTF9CRV9GSVJFRCcpO1xuICAgICAgICB9IGNhdGNoIChleCkge1xuICAgICAgICAgIGVycm9yJDEgPSBleDtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChlcnJvciQxICYmICEoZXJyb3IkMSBpbnN0YW5jZW9mIEVycm9yKSkge1xuICAgICAgICAgIHNldEN1cnJlbnRseVZhbGlkYXRpbmdFbGVtZW50JDEoZWxlbWVudCk7XG5cbiAgICAgICAgICBlcnJvcignJXM6IHR5cGUgc3BlY2lmaWNhdGlvbiBvZiAlcycgKyAnIGAlc2AgaXMgaW52YWxpZDsgdGhlIHR5cGUgY2hlY2tlciAnICsgJ2Z1bmN0aW9uIG11c3QgcmV0dXJuIGBudWxsYCBvciBhbiBgRXJyb3JgIGJ1dCByZXR1cm5lZCBhICVzLiAnICsgJ1lvdSBtYXkgaGF2ZSBmb3Jnb3R0ZW4gdG8gcGFzcyBhbiBhcmd1bWVudCB0byB0aGUgdHlwZSBjaGVja2VyICcgKyAnY3JlYXRvciAoYXJyYXlPZiwgaW5zdGFuY2VPZiwgb2JqZWN0T2YsIG9uZU9mLCBvbmVPZlR5cGUsIGFuZCAnICsgJ3NoYXBlIGFsbCByZXF1aXJlIGFuIGFyZ3VtZW50KS4nLCBjb21wb25lbnROYW1lIHx8ICdSZWFjdCBjbGFzcycsIGxvY2F0aW9uLCB0eXBlU3BlY05hbWUsIHR5cGVvZiBlcnJvciQxKTtcblxuICAgICAgICAgIHNldEN1cnJlbnRseVZhbGlkYXRpbmdFbGVtZW50JDEobnVsbCk7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoZXJyb3IkMSBpbnN0YW5jZW9mIEVycm9yICYmICEoZXJyb3IkMS5tZXNzYWdlIGluIGxvZ2dlZFR5cGVGYWlsdXJlcykpIHtcbiAgICAgICAgICAvLyBPbmx5IG1vbml0b3IgdGhpcyBmYWlsdXJlIG9uY2UgYmVjYXVzZSB0aGVyZSB0ZW5kcyB0byBiZSBhIGxvdCBvZiB0aGVcbiAgICAgICAgICAvLyBzYW1lIGVycm9yLlxuICAgICAgICAgIGxvZ2dlZFR5cGVGYWlsdXJlc1tlcnJvciQxLm1lc3NhZ2VdID0gdHJ1ZTtcbiAgICAgICAgICBzZXRDdXJyZW50bHlWYWxpZGF0aW5nRWxlbWVudCQxKGVsZW1lbnQpO1xuXG4gICAgICAgICAgZXJyb3IoJ0ZhaWxlZCAlcyB0eXBlOiAlcycsIGxvY2F0aW9uLCBlcnJvciQxLm1lc3NhZ2UpO1xuXG4gICAgICAgICAgc2V0Q3VycmVudGx5VmFsaWRhdGluZ0VsZW1lbnQkMShudWxsKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG52YXIgaXNBcnJheUltcGwgPSBBcnJheS5pc0FycmF5OyAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tcmVkZWNsYXJlXG5cbmZ1bmN0aW9uIGlzQXJyYXkoYSkge1xuICByZXR1cm4gaXNBcnJheUltcGwoYSk7XG59XG5cbi8qXG4gKiBUaGUgYCcnICsgdmFsdWVgIHBhdHRlcm4gKHVzZWQgaW4gcGVyZi1zZW5zaXRpdmUgY29kZSkgdGhyb3dzIGZvciBTeW1ib2xcbiAqIGFuZCBUZW1wb3JhbC4qIHR5cGVzLiBTZWUgaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlYWN0L3B1bGwvMjIwNjQuXG4gKlxuICogVGhlIGZ1bmN0aW9ucyBpbiB0aGlzIG1vZHVsZSB3aWxsIHRocm93IGFuIGVhc2llci10by11bmRlcnN0YW5kLFxuICogZWFzaWVyLXRvLWRlYnVnIGV4Y2VwdGlvbiB3aXRoIGEgY2xlYXIgZXJyb3JzIG1lc3NhZ2UgbWVzc2FnZSBleHBsYWluaW5nIHRoZVxuICogcHJvYmxlbS4gKEluc3RlYWQgb2YgYSBjb25mdXNpbmcgZXhjZXB0aW9uIHRocm93biBpbnNpZGUgdGhlIGltcGxlbWVudGF0aW9uXG4gKiBvZiB0aGUgYHZhbHVlYCBvYmplY3QpLlxuICovXG4vLyAkRmxvd0ZpeE1lW2luY29tcGF0aWJsZS1yZXR1cm5dIG9ubHkgY2FsbGVkIGluIERFViwgc28gdm9pZCByZXR1cm4gaXMgbm90IHBvc3NpYmxlLlxuZnVuY3Rpb24gdHlwZU5hbWUodmFsdWUpIHtcbiAge1xuICAgIC8vIHRvU3RyaW5nVGFnIGlzIG5lZWRlZCBmb3IgbmFtZXNwYWNlZCB0eXBlcyBsaWtlIFRlbXBvcmFsLkluc3RhbnRcbiAgICB2YXIgaGFzVG9TdHJpbmdUYWcgPSB0eXBlb2YgU3ltYm9sID09PSAnZnVuY3Rpb24nICYmIFN5bWJvbC50b1N0cmluZ1RhZztcbiAgICB2YXIgdHlwZSA9IGhhc1RvU3RyaW5nVGFnICYmIHZhbHVlW1N5bWJvbC50b1N0cmluZ1RhZ10gfHwgdmFsdWUuY29uc3RydWN0b3IubmFtZSB8fCAnT2JqZWN0JzsgLy8gJEZsb3dGaXhNZVtpbmNvbXBhdGlibGUtcmV0dXJuXVxuXG4gICAgcmV0dXJuIHR5cGU7XG4gIH1cbn0gLy8gJEZsb3dGaXhNZVtpbmNvbXBhdGlibGUtcmV0dXJuXSBvbmx5IGNhbGxlZCBpbiBERVYsIHNvIHZvaWQgcmV0dXJuIGlzIG5vdCBwb3NzaWJsZS5cblxuXG5mdW5jdGlvbiB3aWxsQ29lcmNpb25UaHJvdyh2YWx1ZSkge1xuICB7XG4gICAgdHJ5IHtcbiAgICAgIHRlc3RTdHJpbmdDb2VyY2lvbih2YWx1ZSk7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfSBjYXRjaCAoZSkge1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICB9XG59XG5cbmZ1bmN0aW9uIHRlc3RTdHJpbmdDb2VyY2lvbih2YWx1ZSkge1xuICAvLyBJZiB5b3UgZW5kZWQgdXAgaGVyZSBieSBmb2xsb3dpbmcgYW4gZXhjZXB0aW9uIGNhbGwgc3RhY2ssIGhlcmUncyB3aGF0J3NcbiAgLy8gaGFwcGVuZWQ6IHlvdSBzdXBwbGllZCBhbiBvYmplY3Qgb3Igc3ltYm9sIHZhbHVlIHRvIFJlYWN0IChhcyBhIHByb3AsIGtleSxcbiAgLy8gRE9NIGF0dHJpYnV0ZSwgQ1NTIHByb3BlcnR5LCBzdHJpbmcgcmVmLCBldGMuKSBhbmQgd2hlbiBSZWFjdCB0cmllZCB0b1xuICAvLyBjb2VyY2UgaXQgdG8gYSBzdHJpbmcgdXNpbmcgYCcnICsgdmFsdWVgLCBhbiBleGNlcHRpb24gd2FzIHRocm93bi5cbiAgLy9cbiAgLy8gVGhlIG1vc3QgY29tbW9uIHR5cGVzIHRoYXQgd2lsbCBjYXVzZSB0aGlzIGV4Y2VwdGlvbiBhcmUgYFN5bWJvbGAgaW5zdGFuY2VzXG4gIC8vIGFuZCBUZW1wb3JhbCBvYmplY3RzIGxpa2UgYFRlbXBvcmFsLkluc3RhbnRgLiBCdXQgYW55IG9iamVjdCB0aGF0IGhhcyBhXG4gIC8vIGB2YWx1ZU9mYCBvciBgW1N5bWJvbC50b1ByaW1pdGl2ZV1gIG1ldGhvZCB0aGF0IHRocm93cyB3aWxsIGFsc28gY2F1c2UgdGhpc1xuICAvLyBleGNlcHRpb24uIChMaWJyYXJ5IGF1dGhvcnMgZG8gdGhpcyB0byBwcmV2ZW50IHVzZXJzIGZyb20gdXNpbmcgYnVpbHQtaW5cbiAgLy8gbnVtZXJpYyBvcGVyYXRvcnMgbGlrZSBgK2Agb3IgY29tcGFyaXNvbiBvcGVyYXRvcnMgbGlrZSBgPj1gIGJlY2F1c2UgY3VzdG9tXG4gIC8vIG1ldGhvZHMgYXJlIG5lZWRlZCB0byBwZXJmb3JtIGFjY3VyYXRlIGFyaXRobWV0aWMgb3IgY29tcGFyaXNvbi4pXG4gIC8vXG4gIC8vIFRvIGZpeCB0aGUgcHJvYmxlbSwgY29lcmNlIHRoaXMgb2JqZWN0IG9yIHN5bWJvbCB2YWx1ZSB0byBhIHN0cmluZyBiZWZvcmVcbiAgLy8gcGFzc2luZyBpdCB0byBSZWFjdC4gVGhlIG1vc3QgcmVsaWFibGUgd2F5IGlzIHVzdWFsbHkgYFN0cmluZyh2YWx1ZSlgLlxuICAvL1xuICAvLyBUbyBmaW5kIHdoaWNoIHZhbHVlIGlzIHRocm93aW5nLCBjaGVjayB0aGUgYnJvd3NlciBvciBkZWJ1Z2dlciBjb25zb2xlLlxuICAvLyBCZWZvcmUgdGhpcyBleGNlcHRpb24gd2FzIHRocm93biwgdGhlcmUgc2hvdWxkIGJlIGBjb25zb2xlLmVycm9yYCBvdXRwdXRcbiAgLy8gdGhhdCBzaG93cyB0aGUgdHlwZSAoU3ltYm9sLCBUZW1wb3JhbC5QbGFpbkRhdGUsIGV0Yy4pIHRoYXQgY2F1c2VkIHRoZVxuICAvLyBwcm9ibGVtIGFuZCBob3cgdGhhdCB0eXBlIHdhcyB1c2VkOiBrZXksIGF0cnJpYnV0ZSwgaW5wdXQgdmFsdWUgcHJvcCwgZXRjLlxuICAvLyBJbiBtb3N0IGNhc2VzLCB0aGlzIGNvbnNvbGUgb3V0cHV0IGFsc28gc2hvd3MgdGhlIGNvbXBvbmVudCBhbmQgaXRzXG4gIC8vIGFuY2VzdG9yIGNvbXBvbmVudHMgd2hlcmUgdGhlIGV4Y2VwdGlvbiBoYXBwZW5lZC5cbiAgLy9cbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWludGVybmFsL3NhZmUtc3RyaW5nLWNvZXJjaW9uXG4gIHJldHVybiAnJyArIHZhbHVlO1xufVxuZnVuY3Rpb24gY2hlY2tLZXlTdHJpbmdDb2VyY2lvbih2YWx1ZSkge1xuICB7XG4gICAgaWYgKHdpbGxDb2VyY2lvblRocm93KHZhbHVlKSkge1xuICAgICAgZXJyb3IoJ1RoZSBwcm92aWRlZCBrZXkgaXMgYW4gdW5zdXBwb3J0ZWQgdHlwZSAlcy4nICsgJyBUaGlzIHZhbHVlIG11c3QgYmUgY29lcmNlZCB0byBhIHN0cmluZyBiZWZvcmUgdXNpbmcgaXQgaGVyZS4nLCB0eXBlTmFtZSh2YWx1ZSkpO1xuXG4gICAgICByZXR1cm4gdGVzdFN0cmluZ0NvZXJjaW9uKHZhbHVlKTsgLy8gdGhyb3cgKHRvIGhlbHAgY2FsbGVycyBmaW5kIHRyb3VibGVzaG9vdGluZyBjb21tZW50cylcbiAgICB9XG4gIH1cbn1cblxudmFyIFJlYWN0Q3VycmVudE93bmVyJDEgPSBSZWFjdFNoYXJlZEludGVybmFscy5SZWFjdEN1cnJlbnRPd25lcjtcbnZhciBSRVNFUlZFRF9QUk9QUyA9IHtcbiAga2V5OiB0cnVlLFxuICByZWY6IHRydWUsXG4gIF9fc2VsZjogdHJ1ZSxcbiAgX19zb3VyY2U6IHRydWVcbn07XG52YXIgc3BlY2lhbFByb3BLZXlXYXJuaW5nU2hvd247XG52YXIgc3BlY2lhbFByb3BSZWZXYXJuaW5nU2hvd247XG52YXIgZGlkV2FybkFib3V0U3RyaW5nUmVmcztcblxue1xuICBkaWRXYXJuQWJvdXRTdHJpbmdSZWZzID0ge307XG59XG5cbmZ1bmN0aW9uIGhhc1ZhbGlkUmVmKGNvbmZpZykge1xuICB7XG4gICAgaWYgKGhhc093blByb3BlcnR5LmNhbGwoY29uZmlnLCAncmVmJykpIHtcbiAgICAgIHZhciBnZXR0ZXIgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKGNvbmZpZywgJ3JlZicpLmdldDtcblxuICAgICAgaWYgKGdldHRlciAmJiBnZXR0ZXIuaXNSZWFjdFdhcm5pbmcpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiBjb25maWcucmVmICE9PSB1bmRlZmluZWQ7XG59XG5cbmZ1bmN0aW9uIGhhc1ZhbGlkS2V5KGNvbmZpZykge1xuICB7XG4gICAgaWYgKGhhc093blByb3BlcnR5LmNhbGwoY29uZmlnLCAna2V5JykpIHtcbiAgICAgIHZhciBnZXR0ZXIgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKGNvbmZpZywgJ2tleScpLmdldDtcblxuICAgICAgaWYgKGdldHRlciAmJiBnZXR0ZXIuaXNSZWFjdFdhcm5pbmcpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiBjb25maWcua2V5ICE9PSB1bmRlZmluZWQ7XG59XG5cbmZ1bmN0aW9uIHdhcm5JZlN0cmluZ1JlZkNhbm5vdEJlQXV0b0NvbnZlcnRlZChjb25maWcsIHNlbGYpIHtcbiAge1xuICAgIGlmICh0eXBlb2YgY29uZmlnLnJlZiA9PT0gJ3N0cmluZycgJiYgUmVhY3RDdXJyZW50T3duZXIkMS5jdXJyZW50ICYmIHNlbGYgJiYgUmVhY3RDdXJyZW50T3duZXIkMS5jdXJyZW50LnN0YXRlTm9kZSAhPT0gc2VsZikge1xuICAgICAgdmFyIGNvbXBvbmVudE5hbWUgPSBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUoUmVhY3RDdXJyZW50T3duZXIkMS5jdXJyZW50LnR5cGUpO1xuXG4gICAgICBpZiAoIWRpZFdhcm5BYm91dFN0cmluZ1JlZnNbY29tcG9uZW50TmFtZV0pIHtcbiAgICAgICAgZXJyb3IoJ0NvbXBvbmVudCBcIiVzXCIgY29udGFpbnMgdGhlIHN0cmluZyByZWYgXCIlc1wiLiAnICsgJ1N1cHBvcnQgZm9yIHN0cmluZyByZWZzIHdpbGwgYmUgcmVtb3ZlZCBpbiBhIGZ1dHVyZSBtYWpvciByZWxlYXNlLiAnICsgJ1RoaXMgY2FzZSBjYW5ub3QgYmUgYXV0b21hdGljYWxseSBjb252ZXJ0ZWQgdG8gYW4gYXJyb3cgZnVuY3Rpb24uICcgKyAnV2UgYXNrIHlvdSB0byBtYW51YWxseSBmaXggdGhpcyBjYXNlIGJ5IHVzaW5nIHVzZVJlZigpIG9yIGNyZWF0ZVJlZigpIGluc3RlYWQuICcgKyAnTGVhcm4gbW9yZSBhYm91dCB1c2luZyByZWZzIHNhZmVseSBoZXJlOiAnICsgJ2h0dHBzOi8vcmVhY3Rqcy5vcmcvbGluay9zdHJpY3QtbW9kZS1zdHJpbmctcmVmJywgZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKFJlYWN0Q3VycmVudE93bmVyJDEuY3VycmVudC50eXBlKSwgY29uZmlnLnJlZik7XG5cbiAgICAgICAgZGlkV2FybkFib3V0U3RyaW5nUmVmc1tjb21wb25lbnROYW1lXSA9IHRydWU7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbmZ1bmN0aW9uIGRlZmluZUtleVByb3BXYXJuaW5nR2V0dGVyKHByb3BzLCBkaXNwbGF5TmFtZSkge1xuICB7XG4gICAgdmFyIHdhcm5BYm91dEFjY2Vzc2luZ0tleSA9IGZ1bmN0aW9uICgpIHtcbiAgICAgIGlmICghc3BlY2lhbFByb3BLZXlXYXJuaW5nU2hvd24pIHtcbiAgICAgICAgc3BlY2lhbFByb3BLZXlXYXJuaW5nU2hvd24gPSB0cnVlO1xuXG4gICAgICAgIGVycm9yKCclczogYGtleWAgaXMgbm90IGEgcHJvcC4gVHJ5aW5nIHRvIGFjY2VzcyBpdCB3aWxsIHJlc3VsdCAnICsgJ2luIGB1bmRlZmluZWRgIGJlaW5nIHJldHVybmVkLiBJZiB5b3UgbmVlZCB0byBhY2Nlc3MgdGhlIHNhbWUgJyArICd2YWx1ZSB3aXRoaW4gdGhlIGNoaWxkIGNvbXBvbmVudCwgeW91IHNob3VsZCBwYXNzIGl0IGFzIGEgZGlmZmVyZW50ICcgKyAncHJvcC4gKGh0dHBzOi8vcmVhY3Rqcy5vcmcvbGluay9zcGVjaWFsLXByb3BzKScsIGRpc3BsYXlOYW1lKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgd2FybkFib3V0QWNjZXNzaW5nS2V5LmlzUmVhY3RXYXJuaW5nID0gdHJ1ZTtcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkocHJvcHMsICdrZXknLCB7XG4gICAgICBnZXQ6IHdhcm5BYm91dEFjY2Vzc2luZ0tleSxcbiAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgIH0pO1xuICB9XG59XG5cbmZ1bmN0aW9uIGRlZmluZVJlZlByb3BXYXJuaW5nR2V0dGVyKHByb3BzLCBkaXNwbGF5TmFtZSkge1xuICB7XG4gICAgdmFyIHdhcm5BYm91dEFjY2Vzc2luZ1JlZiA9IGZ1bmN0aW9uICgpIHtcbiAgICAgIGlmICghc3BlY2lhbFByb3BSZWZXYXJuaW5nU2hvd24pIHtcbiAgICAgICAgc3BlY2lhbFByb3BSZWZXYXJuaW5nU2hvd24gPSB0cnVlO1xuXG4gICAgICAgIGVycm9yKCclczogYHJlZmAgaXMgbm90IGEgcHJvcC4gVHJ5aW5nIHRvIGFjY2VzcyBpdCB3aWxsIHJlc3VsdCAnICsgJ2luIGB1bmRlZmluZWRgIGJlaW5nIHJldHVybmVkLiBJZiB5b3UgbmVlZCB0byBhY2Nlc3MgdGhlIHNhbWUgJyArICd2YWx1ZSB3aXRoaW4gdGhlIGNoaWxkIGNvbXBvbmVudCwgeW91IHNob3VsZCBwYXNzIGl0IGFzIGEgZGlmZmVyZW50ICcgKyAncHJvcC4gKGh0dHBzOi8vcmVhY3Rqcy5vcmcvbGluay9zcGVjaWFsLXByb3BzKScsIGRpc3BsYXlOYW1lKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgd2FybkFib3V0QWNjZXNzaW5nUmVmLmlzUmVhY3RXYXJuaW5nID0gdHJ1ZTtcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkocHJvcHMsICdyZWYnLCB7XG4gICAgICBnZXQ6IHdhcm5BYm91dEFjY2Vzc2luZ1JlZixcbiAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgIH0pO1xuICB9XG59XG4vKipcbiAqIEZhY3RvcnkgbWV0aG9kIHRvIGNyZWF0ZSBhIG5ldyBSZWFjdCBlbGVtZW50LiBUaGlzIG5vIGxvbmdlciBhZGhlcmVzIHRvXG4gKiB0aGUgY2xhc3MgcGF0dGVybiwgc28gZG8gbm90IHVzZSBuZXcgdG8gY2FsbCBpdC4gQWxzbywgaW5zdGFuY2VvZiBjaGVja1xuICogd2lsbCBub3Qgd29yay4gSW5zdGVhZCB0ZXN0ICQkdHlwZW9mIGZpZWxkIGFnYWluc3QgU3ltYm9sLmZvcigncmVhY3QuZWxlbWVudCcpIHRvIGNoZWNrXG4gKiBpZiBzb21ldGhpbmcgaXMgYSBSZWFjdCBFbGVtZW50LlxuICpcbiAqIEBwYXJhbSB7Kn0gdHlwZVxuICogQHBhcmFtIHsqfSBwcm9wc1xuICogQHBhcmFtIHsqfSBrZXlcbiAqIEBwYXJhbSB7c3RyaW5nfG9iamVjdH0gcmVmXG4gKiBAcGFyYW0geyp9IG93bmVyXG4gKiBAcGFyYW0geyp9IHNlbGYgQSAqdGVtcG9yYXJ5KiBoZWxwZXIgdG8gZGV0ZWN0IHBsYWNlcyB3aGVyZSBgdGhpc2AgaXNcbiAqIGRpZmZlcmVudCBmcm9tIHRoZSBgb3duZXJgIHdoZW4gUmVhY3QuY3JlYXRlRWxlbWVudCBpcyBjYWxsZWQsIHNvIHRoYXQgd2VcbiAqIGNhbiB3YXJuLiBXZSB3YW50IHRvIGdldCByaWQgb2Ygb3duZXIgYW5kIHJlcGxhY2Ugc3RyaW5nIGByZWZgcyB3aXRoIGFycm93XG4gKiBmdW5jdGlvbnMsIGFuZCBhcyBsb25nIGFzIGB0aGlzYCBhbmQgb3duZXIgYXJlIHRoZSBzYW1lLCB0aGVyZSB3aWxsIGJlIG5vXG4gKiBjaGFuZ2UgaW4gYmVoYXZpb3IuXG4gKiBAcGFyYW0geyp9IHNvdXJjZSBBbiBhbm5vdGF0aW9uIG9iamVjdCAoYWRkZWQgYnkgYSB0cmFuc3BpbGVyIG9yIG90aGVyd2lzZSlcbiAqIGluZGljYXRpbmcgZmlsZW5hbWUsIGxpbmUgbnVtYmVyLCBhbmQvb3Igb3RoZXIgaW5mb3JtYXRpb24uXG4gKiBAaW50ZXJuYWxcbiAqL1xuXG5cbmZ1bmN0aW9uIFJlYWN0RWxlbWVudCh0eXBlLCBrZXksIHJlZiwgc2VsZiwgc291cmNlLCBvd25lciwgcHJvcHMpIHtcbiAgdmFyIGVsZW1lbnQgPSB7XG4gICAgLy8gVGhpcyB0YWcgYWxsb3dzIHVzIHRvIHVuaXF1ZWx5IGlkZW50aWZ5IHRoaXMgYXMgYSBSZWFjdCBFbGVtZW50XG4gICAgJCR0eXBlb2Y6IFJFQUNUX0VMRU1FTlRfVFlQRSxcbiAgICAvLyBCdWlsdC1pbiBwcm9wZXJ0aWVzIHRoYXQgYmVsb25nIG9uIHRoZSBlbGVtZW50XG4gICAgdHlwZTogdHlwZSxcbiAgICBrZXk6IGtleSxcbiAgICByZWY6IHJlZixcbiAgICBwcm9wczogcHJvcHMsXG4gICAgLy8gUmVjb3JkIHRoZSBjb21wb25lbnQgcmVzcG9uc2libGUgZm9yIGNyZWF0aW5nIHRoaXMgZWxlbWVudC5cbiAgICBfb3duZXI6IG93bmVyXG4gIH07XG5cbiAge1xuICAgIC8vIFRoZSB2YWxpZGF0aW9uIGZsYWcgaXMgY3VycmVudGx5IG11dGF0aXZlLiBXZSBwdXQgaXQgb25cbiAgICAvLyBhbiBleHRlcm5hbCBiYWNraW5nIHN0b3JlIHNvIHRoYXQgd2UgY2FuIGZyZWV6ZSB0aGUgd2hvbGUgb2JqZWN0LlxuICAgIC8vIFRoaXMgY2FuIGJlIHJlcGxhY2VkIHdpdGggYSBXZWFrTWFwIG9uY2UgdGhleSBhcmUgaW1wbGVtZW50ZWQgaW5cbiAgICAvLyBjb21tb25seSB1c2VkIGRldmVsb3BtZW50IGVudmlyb25tZW50cy5cbiAgICBlbGVtZW50Ll9zdG9yZSA9IHt9OyAvLyBUbyBtYWtlIGNvbXBhcmluZyBSZWFjdEVsZW1lbnRzIGVhc2llciBmb3IgdGVzdGluZyBwdXJwb3Nlcywgd2UgbWFrZVxuICAgIC8vIHRoZSB2YWxpZGF0aW9uIGZsYWcgbm9uLWVudW1lcmFibGUgKHdoZXJlIHBvc3NpYmxlLCB3aGljaCBzaG91bGRcbiAgICAvLyBpbmNsdWRlIGV2ZXJ5IGVudmlyb25tZW50IHdlIHJ1biB0ZXN0cyBpbiksIHNvIHRoZSB0ZXN0IGZyYW1ld29ya1xuICAgIC8vIGlnbm9yZXMgaXQuXG5cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoZWxlbWVudC5fc3RvcmUsICd2YWxpZGF0ZWQnLCB7XG4gICAgICBjb25maWd1cmFibGU6IGZhbHNlLFxuICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgIHZhbHVlOiBmYWxzZVxuICAgIH0pOyAvLyBzZWxmIGFuZCBzb3VyY2UgYXJlIERFViBvbmx5IHByb3BlcnRpZXMuXG5cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoZWxlbWVudCwgJ19zZWxmJywge1xuICAgICAgY29uZmlndXJhYmxlOiBmYWxzZSxcbiAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgd3JpdGFibGU6IGZhbHNlLFxuICAgICAgdmFsdWU6IHNlbGZcbiAgICB9KTsgLy8gVHdvIGVsZW1lbnRzIGNyZWF0ZWQgaW4gdHdvIGRpZmZlcmVudCBwbGFjZXMgc2hvdWxkIGJlIGNvbnNpZGVyZWRcbiAgICAvLyBlcXVhbCBmb3IgdGVzdGluZyBwdXJwb3NlcyBhbmQgdGhlcmVmb3JlIHdlIGhpZGUgaXQgZnJvbSBlbnVtZXJhdGlvbi5cblxuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShlbGVtZW50LCAnX3NvdXJjZScsIHtcbiAgICAgIGNvbmZpZ3VyYWJsZTogZmFsc2UsXG4gICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgIHdyaXRhYmxlOiBmYWxzZSxcbiAgICAgIHZhbHVlOiBzb3VyY2VcbiAgICB9KTtcblxuICAgIGlmIChPYmplY3QuZnJlZXplKSB7XG4gICAgICBPYmplY3QuZnJlZXplKGVsZW1lbnQucHJvcHMpO1xuICAgICAgT2JqZWN0LmZyZWV6ZShlbGVtZW50KTtcbiAgICB9XG4gIH1cblxuICByZXR1cm4gZWxlbWVudDtcbn1cbi8qKlxuICogaHR0cHM6Ly9naXRodWIuY29tL3JlYWN0anMvcmZjcy9wdWxsLzEwN1xuICogQHBhcmFtIHsqfSB0eXBlXG4gKiBAcGFyYW0ge29iamVjdH0gcHJvcHNcbiAqIEBwYXJhbSB7c3RyaW5nfSBrZXlcbiAqL1xuXG5mdW5jdGlvbiBqc3hERVYkMSh0eXBlLCBjb25maWcsIG1heWJlS2V5LCBzb3VyY2UsIHNlbGYpIHtcbiAge1xuICAgIHZhciBwcm9wTmFtZTsgLy8gUmVzZXJ2ZWQgbmFtZXMgYXJlIGV4dHJhY3RlZFxuXG4gICAgdmFyIHByb3BzID0ge307XG4gICAgdmFyIGtleSA9IG51bGw7XG4gICAgdmFyIHJlZiA9IG51bGw7IC8vIEN1cnJlbnRseSwga2V5IGNhbiBiZSBzcHJlYWQgaW4gYXMgYSBwcm9wLiBUaGlzIGNhdXNlcyBhIHBvdGVudGlhbFxuICAgIC8vIGlzc3VlIGlmIGtleSBpcyBhbHNvIGV4cGxpY2l0bHkgZGVjbGFyZWQgKGllLiA8ZGl2IHsuLi5wcm9wc30ga2V5PVwiSGlcIiAvPlxuICAgIC8vIG9yIDxkaXYga2V5PVwiSGlcIiB7Li4ucHJvcHN9IC8+ICkuIFdlIHdhbnQgdG8gZGVwcmVjYXRlIGtleSBzcHJlYWQsXG4gICAgLy8gYnV0IGFzIGFuIGludGVybWVkaWFyeSBzdGVwLCB3ZSB3aWxsIHVzZSBqc3hERVYgZm9yIGV2ZXJ5dGhpbmcgZXhjZXB0XG4gICAgLy8gPGRpdiB7Li4ucHJvcHN9IGtleT1cIkhpXCIgLz4sIGJlY2F1c2Ugd2UgYXJlbid0IGN1cnJlbnRseSBhYmxlIHRvIHRlbGwgaWZcbiAgICAvLyBrZXkgaXMgZXhwbGljaXRseSBkZWNsYXJlZCB0byBiZSB1bmRlZmluZWQgb3Igbm90LlxuXG4gICAgaWYgKG1heWJlS2V5ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIHtcbiAgICAgICAgY2hlY2tLZXlTdHJpbmdDb2VyY2lvbihtYXliZUtleSk7XG4gICAgICB9XG5cbiAgICAgIGtleSA9ICcnICsgbWF5YmVLZXk7XG4gICAgfVxuXG4gICAgaWYgKGhhc1ZhbGlkS2V5KGNvbmZpZykpIHtcbiAgICAgIHtcbiAgICAgICAgY2hlY2tLZXlTdHJpbmdDb2VyY2lvbihjb25maWcua2V5KTtcbiAgICAgIH1cblxuICAgICAga2V5ID0gJycgKyBjb25maWcua2V5O1xuICAgIH1cblxuICAgIGlmIChoYXNWYWxpZFJlZihjb25maWcpKSB7XG4gICAgICByZWYgPSBjb25maWcucmVmO1xuICAgICAgd2FybklmU3RyaW5nUmVmQ2Fubm90QmVBdXRvQ29udmVydGVkKGNvbmZpZywgc2VsZik7XG4gICAgfSAvLyBSZW1haW5pbmcgcHJvcGVydGllcyBhcmUgYWRkZWQgdG8gYSBuZXcgcHJvcHMgb2JqZWN0XG5cblxuICAgIGZvciAocHJvcE5hbWUgaW4gY29uZmlnKSB7XG4gICAgICBpZiAoaGFzT3duUHJvcGVydHkuY2FsbChjb25maWcsIHByb3BOYW1lKSAmJiAhUkVTRVJWRURfUFJPUFMuaGFzT3duUHJvcGVydHkocHJvcE5hbWUpKSB7XG4gICAgICAgIHByb3BzW3Byb3BOYW1lXSA9IGNvbmZpZ1twcm9wTmFtZV07XG4gICAgICB9XG4gICAgfSAvLyBSZXNvbHZlIGRlZmF1bHQgcHJvcHNcblxuXG4gICAgaWYgKHR5cGUgJiYgdHlwZS5kZWZhdWx0UHJvcHMpIHtcbiAgICAgIHZhciBkZWZhdWx0UHJvcHMgPSB0eXBlLmRlZmF1bHRQcm9wcztcblxuICAgICAgZm9yIChwcm9wTmFtZSBpbiBkZWZhdWx0UHJvcHMpIHtcbiAgICAgICAgaWYgKHByb3BzW3Byb3BOYW1lXSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgcHJvcHNbcHJvcE5hbWVdID0gZGVmYXVsdFByb3BzW3Byb3BOYW1lXTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIGlmIChrZXkgfHwgcmVmKSB7XG4gICAgICB2YXIgZGlzcGxheU5hbWUgPSB0eXBlb2YgdHlwZSA9PT0gJ2Z1bmN0aW9uJyA/IHR5cGUuZGlzcGxheU5hbWUgfHwgdHlwZS5uYW1lIHx8ICdVbmtub3duJyA6IHR5cGU7XG5cbiAgICAgIGlmIChrZXkpIHtcbiAgICAgICAgZGVmaW5lS2V5UHJvcFdhcm5pbmdHZXR0ZXIocHJvcHMsIGRpc3BsYXlOYW1lKTtcbiAgICAgIH1cblxuICAgICAgaWYgKHJlZikge1xuICAgICAgICBkZWZpbmVSZWZQcm9wV2FybmluZ0dldHRlcihwcm9wcywgZGlzcGxheU5hbWUpO1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiBSZWFjdEVsZW1lbnQodHlwZSwga2V5LCByZWYsIHNlbGYsIHNvdXJjZSwgUmVhY3RDdXJyZW50T3duZXIkMS5jdXJyZW50LCBwcm9wcyk7XG4gIH1cbn1cblxudmFyIFJlYWN0Q3VycmVudE93bmVyID0gUmVhY3RTaGFyZWRJbnRlcm5hbHMuUmVhY3RDdXJyZW50T3duZXI7XG52YXIgUmVhY3REZWJ1Z0N1cnJlbnRGcmFtZSA9IFJlYWN0U2hhcmVkSW50ZXJuYWxzLlJlYWN0RGVidWdDdXJyZW50RnJhbWU7XG52YXIgUkVBQ1RfQ0xJRU5UX1JFRkVSRU5DRSA9IFN5bWJvbC5mb3IoJ3JlYWN0LmNsaWVudC5yZWZlcmVuY2UnKTtcblxuZnVuY3Rpb24gc2V0Q3VycmVudGx5VmFsaWRhdGluZ0VsZW1lbnQoZWxlbWVudCkge1xuICB7XG4gICAgaWYgKGVsZW1lbnQpIHtcbiAgICAgIHZhciBvd25lciA9IGVsZW1lbnQuX293bmVyO1xuICAgICAgdmFyIHN0YWNrID0gZGVzY3JpYmVVbmtub3duRWxlbWVudFR5cGVGcmFtZUluREVWKGVsZW1lbnQudHlwZSwgZWxlbWVudC5fc291cmNlLCBvd25lciA/IG93bmVyLnR5cGUgOiBudWxsKTtcbiAgICAgIFJlYWN0RGVidWdDdXJyZW50RnJhbWUuc2V0RXh0cmFTdGFja0ZyYW1lKHN0YWNrKTtcbiAgICB9IGVsc2Uge1xuICAgICAgUmVhY3REZWJ1Z0N1cnJlbnRGcmFtZS5zZXRFeHRyYVN0YWNrRnJhbWUobnVsbCk7XG4gICAgfVxuICB9XG59XG5cbnZhciBwcm9wVHlwZXNNaXNzcGVsbFdhcm5pbmdTaG93bjtcblxue1xuICBwcm9wVHlwZXNNaXNzcGVsbFdhcm5pbmdTaG93biA9IGZhbHNlO1xufVxuLyoqXG4gKiBWZXJpZmllcyB0aGUgb2JqZWN0IGlzIGEgUmVhY3RFbGVtZW50LlxuICogU2VlIGh0dHBzOi8vcmVhY3Rqcy5vcmcvZG9jcy9yZWFjdC1hcGkuaHRtbCNpc3ZhbGlkZWxlbWVudFxuICogQHBhcmFtIHs/b2JqZWN0fSBvYmplY3RcbiAqIEByZXR1cm4ge2Jvb2xlYW59IFRydWUgaWYgYG9iamVjdGAgaXMgYSBSZWFjdEVsZW1lbnQuXG4gKiBAZmluYWxcbiAqL1xuXG5cbmZ1bmN0aW9uIGlzVmFsaWRFbGVtZW50KG9iamVjdCkge1xuICB7XG4gICAgcmV0dXJuIHR5cGVvZiBvYmplY3QgPT09ICdvYmplY3QnICYmIG9iamVjdCAhPT0gbnVsbCAmJiBvYmplY3QuJCR0eXBlb2YgPT09IFJFQUNUX0VMRU1FTlRfVFlQRTtcbiAgfVxufVxuXG5mdW5jdGlvbiBnZXREZWNsYXJhdGlvbkVycm9yQWRkZW5kdW0oKSB7XG4gIHtcbiAgICBpZiAoUmVhY3RDdXJyZW50T3duZXIuY3VycmVudCkge1xuICAgICAgdmFyIG5hbWUgPSBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUoUmVhY3RDdXJyZW50T3duZXIuY3VycmVudC50eXBlKTtcblxuICAgICAgaWYgKG5hbWUpIHtcbiAgICAgICAgcmV0dXJuICdcXG5cXG5DaGVjayB0aGUgcmVuZGVyIG1ldGhvZCBvZiBgJyArIG5hbWUgKyAnYC4nO1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiAnJztcbiAgfVxufVxuXG5mdW5jdGlvbiBnZXRTb3VyY2VJbmZvRXJyb3JBZGRlbmR1bShzb3VyY2UpIHtcbiAge1xuICAgIGlmIChzb3VyY2UgIT09IHVuZGVmaW5lZCkge1xuICAgICAgdmFyIGZpbGVOYW1lID0gc291cmNlLmZpbGVOYW1lLnJlcGxhY2UoL14uKltcXFxcXFwvXS8sICcnKTtcbiAgICAgIHZhciBsaW5lTnVtYmVyID0gc291cmNlLmxpbmVOdW1iZXI7XG4gICAgICByZXR1cm4gJ1xcblxcbkNoZWNrIHlvdXIgY29kZSBhdCAnICsgZmlsZU5hbWUgKyAnOicgKyBsaW5lTnVtYmVyICsgJy4nO1xuICAgIH1cblxuICAgIHJldHVybiAnJztcbiAgfVxufVxuLyoqXG4gKiBXYXJuIGlmIHRoZXJlJ3Mgbm8ga2V5IGV4cGxpY2l0bHkgc2V0IG9uIGR5bmFtaWMgYXJyYXlzIG9mIGNoaWxkcmVuIG9yXG4gKiBvYmplY3Qga2V5cyBhcmUgbm90IHZhbGlkLiBUaGlzIGFsbG93cyB1cyB0byBrZWVwIHRyYWNrIG9mIGNoaWxkcmVuIGJldHdlZW5cbiAqIHVwZGF0ZXMuXG4gKi9cblxuXG52YXIgb3duZXJIYXNLZXlVc2VXYXJuaW5nID0ge307XG5cbmZ1bmN0aW9uIGdldEN1cnJlbnRDb21wb25lbnRFcnJvckluZm8ocGFyZW50VHlwZSkge1xuICB7XG4gICAgdmFyIGluZm8gPSBnZXREZWNsYXJhdGlvbkVycm9yQWRkZW5kdW0oKTtcblxuICAgIGlmICghaW5mbykge1xuICAgICAgdmFyIHBhcmVudE5hbWUgPSB0eXBlb2YgcGFyZW50VHlwZSA9PT0gJ3N0cmluZycgPyBwYXJlbnRUeXBlIDogcGFyZW50VHlwZS5kaXNwbGF5TmFtZSB8fCBwYXJlbnRUeXBlLm5hbWU7XG5cbiAgICAgIGlmIChwYXJlbnROYW1lKSB7XG4gICAgICAgIGluZm8gPSBcIlxcblxcbkNoZWNrIHRoZSB0b3AtbGV2ZWwgcmVuZGVyIGNhbGwgdXNpbmcgPFwiICsgcGFyZW50TmFtZSArIFwiPi5cIjtcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gaW5mbztcbiAgfVxufVxuLyoqXG4gKiBXYXJuIGlmIHRoZSBlbGVtZW50IGRvZXNuJ3QgaGF2ZSBhbiBleHBsaWNpdCBrZXkgYXNzaWduZWQgdG8gaXQuXG4gKiBUaGlzIGVsZW1lbnQgaXMgaW4gYW4gYXJyYXkuIFRoZSBhcnJheSBjb3VsZCBncm93IGFuZCBzaHJpbmsgb3IgYmVcbiAqIHJlb3JkZXJlZC4gQWxsIGNoaWxkcmVuIHRoYXQgaGF2ZW4ndCBhbHJlYWR5IGJlZW4gdmFsaWRhdGVkIGFyZSByZXF1aXJlZCB0b1xuICogaGF2ZSBhIFwia2V5XCIgcHJvcGVydHkgYXNzaWduZWQgdG8gaXQuIEVycm9yIHN0YXR1c2VzIGFyZSBjYWNoZWQgc28gYSB3YXJuaW5nXG4gKiB3aWxsIG9ubHkgYmUgc2hvd24gb25jZS5cbiAqXG4gKiBAaW50ZXJuYWxcbiAqIEBwYXJhbSB7UmVhY3RFbGVtZW50fSBlbGVtZW50IEVsZW1lbnQgdGhhdCByZXF1aXJlcyBhIGtleS5cbiAqIEBwYXJhbSB7Kn0gcGFyZW50VHlwZSBlbGVtZW50J3MgcGFyZW50J3MgdHlwZS5cbiAqL1xuXG5cbmZ1bmN0aW9uIHZhbGlkYXRlRXhwbGljaXRLZXkoZWxlbWVudCwgcGFyZW50VHlwZSkge1xuICB7XG4gICAgaWYgKCFlbGVtZW50Ll9zdG9yZSB8fCBlbGVtZW50Ll9zdG9yZS52YWxpZGF0ZWQgfHwgZWxlbWVudC5rZXkgIT0gbnVsbCkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGVsZW1lbnQuX3N0b3JlLnZhbGlkYXRlZCA9IHRydWU7XG4gICAgdmFyIGN1cnJlbnRDb21wb25lbnRFcnJvckluZm8gPSBnZXRDdXJyZW50Q29tcG9uZW50RXJyb3JJbmZvKHBhcmVudFR5cGUpO1xuXG4gICAgaWYgKG93bmVySGFzS2V5VXNlV2FybmluZ1tjdXJyZW50Q29tcG9uZW50RXJyb3JJbmZvXSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIG93bmVySGFzS2V5VXNlV2FybmluZ1tjdXJyZW50Q29tcG9uZW50RXJyb3JJbmZvXSA9IHRydWU7IC8vIFVzdWFsbHkgdGhlIGN1cnJlbnQgb3duZXIgaXMgdGhlIG9mZmVuZGVyLCBidXQgaWYgaXQgYWNjZXB0cyBjaGlsZHJlbiBhcyBhXG4gICAgLy8gcHJvcGVydHksIGl0IG1heSBiZSB0aGUgY3JlYXRvciBvZiB0aGUgY2hpbGQgdGhhdCdzIHJlc3BvbnNpYmxlIGZvclxuICAgIC8vIGFzc2lnbmluZyBpdCBhIGtleS5cblxuICAgIHZhciBjaGlsZE93bmVyID0gJyc7XG5cbiAgICBpZiAoZWxlbWVudCAmJiBlbGVtZW50Ll9vd25lciAmJiBlbGVtZW50Ll9vd25lciAhPT0gUmVhY3RDdXJyZW50T3duZXIuY3VycmVudCkge1xuICAgICAgLy8gR2l2ZSB0aGUgY29tcG9uZW50IHRoYXQgb3JpZ2luYWxseSBjcmVhdGVkIHRoaXMgY2hpbGQuXG4gICAgICBjaGlsZE93bmVyID0gXCIgSXQgd2FzIHBhc3NlZCBhIGNoaWxkIGZyb20gXCIgKyBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUoZWxlbWVudC5fb3duZXIudHlwZSkgKyBcIi5cIjtcbiAgICB9XG5cbiAgICBzZXRDdXJyZW50bHlWYWxpZGF0aW5nRWxlbWVudChlbGVtZW50KTtcblxuICAgIGVycm9yKCdFYWNoIGNoaWxkIGluIGEgbGlzdCBzaG91bGQgaGF2ZSBhIHVuaXF1ZSBcImtleVwiIHByb3AuJyArICclcyVzIFNlZSBodHRwczovL3JlYWN0anMub3JnL2xpbmsvd2FybmluZy1rZXlzIGZvciBtb3JlIGluZm9ybWF0aW9uLicsIGN1cnJlbnRDb21wb25lbnRFcnJvckluZm8sIGNoaWxkT3duZXIpO1xuXG4gICAgc2V0Q3VycmVudGx5VmFsaWRhdGluZ0VsZW1lbnQobnVsbCk7XG4gIH1cbn1cbi8qKlxuICogRW5zdXJlIHRoYXQgZXZlcnkgZWxlbWVudCBlaXRoZXIgaXMgcGFzc2VkIGluIGEgc3RhdGljIGxvY2F0aW9uLCBpbiBhblxuICogYXJyYXkgd2l0aCBhbiBleHBsaWNpdCBrZXlzIHByb3BlcnR5IGRlZmluZWQsIG9yIGluIGFuIG9iamVjdCBsaXRlcmFsXG4gKiB3aXRoIHZhbGlkIGtleSBwcm9wZXJ0eS5cbiAqXG4gKiBAaW50ZXJuYWxcbiAqIEBwYXJhbSB7UmVhY3ROb2RlfSBub2RlIFN0YXRpY2FsbHkgcGFzc2VkIGNoaWxkIG9mIGFueSB0eXBlLlxuICogQHBhcmFtIHsqfSBwYXJlbnRUeXBlIG5vZGUncyBwYXJlbnQncyB0eXBlLlxuICovXG5cblxuZnVuY3Rpb24gdmFsaWRhdGVDaGlsZEtleXMobm9kZSwgcGFyZW50VHlwZSkge1xuICB7XG4gICAgaWYgKHR5cGVvZiBub2RlICE9PSAnb2JqZWN0JyB8fCAhbm9kZSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGlmIChub2RlLiQkdHlwZW9mID09PSBSRUFDVF9DTElFTlRfUkVGRVJFTkNFKSA7IGVsc2UgaWYgKGlzQXJyYXkobm9kZSkpIHtcbiAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbm9kZS5sZW5ndGg7IGkrKykge1xuICAgICAgICB2YXIgY2hpbGQgPSBub2RlW2ldO1xuXG4gICAgICAgIGlmIChpc1ZhbGlkRWxlbWVudChjaGlsZCkpIHtcbiAgICAgICAgICB2YWxpZGF0ZUV4cGxpY2l0S2V5KGNoaWxkLCBwYXJlbnRUeXBlKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoaXNWYWxpZEVsZW1lbnQobm9kZSkpIHtcbiAgICAgIC8vIFRoaXMgZWxlbWVudCB3YXMgcGFzc2VkIGluIGEgdmFsaWQgbG9jYXRpb24uXG4gICAgICBpZiAobm9kZS5fc3RvcmUpIHtcbiAgICAgICAgbm9kZS5fc3RvcmUudmFsaWRhdGVkID0gdHJ1ZTtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgdmFyIGl0ZXJhdG9yRm4gPSBnZXRJdGVyYXRvckZuKG5vZGUpO1xuXG4gICAgICBpZiAodHlwZW9mIGl0ZXJhdG9yRm4gPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgLy8gRW50cnkgaXRlcmF0b3JzIHVzZWQgdG8gcHJvdmlkZSBpbXBsaWNpdCBrZXlzLFxuICAgICAgICAvLyBidXQgbm93IHdlIHByaW50IGEgc2VwYXJhdGUgd2FybmluZyBmb3IgdGhlbSBsYXRlci5cbiAgICAgICAgaWYgKGl0ZXJhdG9yRm4gIT09IG5vZGUuZW50cmllcykge1xuICAgICAgICAgIHZhciBpdGVyYXRvciA9IGl0ZXJhdG9yRm4uY2FsbChub2RlKTtcbiAgICAgICAgICB2YXIgc3RlcDtcblxuICAgICAgICAgIHdoaWxlICghKHN0ZXAgPSBpdGVyYXRvci5uZXh0KCkpLmRvbmUpIHtcbiAgICAgICAgICAgIGlmIChpc1ZhbGlkRWxlbWVudChzdGVwLnZhbHVlKSkge1xuICAgICAgICAgICAgICB2YWxpZGF0ZUV4cGxpY2l0S2V5KHN0ZXAudmFsdWUsIHBhcmVudFR5cGUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuLyoqXG4gKiBHaXZlbiBhbiBlbGVtZW50LCB2YWxpZGF0ZSB0aGF0IGl0cyBwcm9wcyBmb2xsb3cgdGhlIHByb3BUeXBlcyBkZWZpbml0aW9uLFxuICogcHJvdmlkZWQgYnkgdGhlIHR5cGUuXG4gKlxuICogQHBhcmFtIHtSZWFjdEVsZW1lbnR9IGVsZW1lbnRcbiAqL1xuXG5cbmZ1bmN0aW9uIHZhbGlkYXRlUHJvcFR5cGVzKGVsZW1lbnQpIHtcbiAge1xuICAgIHZhciB0eXBlID0gZWxlbWVudC50eXBlO1xuXG4gICAgaWYgKHR5cGUgPT09IG51bGwgfHwgdHlwZSA9PT0gdW5kZWZpbmVkIHx8IHR5cGVvZiB0eXBlID09PSAnc3RyaW5nJykge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGlmICh0eXBlLiQkdHlwZW9mID09PSBSRUFDVF9DTElFTlRfUkVGRVJFTkNFKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdmFyIHByb3BUeXBlcztcblxuICAgIGlmICh0eXBlb2YgdHlwZSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgcHJvcFR5cGVzID0gdHlwZS5wcm9wVHlwZXM7XG4gICAgfSBlbHNlIGlmICh0eXBlb2YgdHlwZSA9PT0gJ29iamVjdCcgJiYgKHR5cGUuJCR0eXBlb2YgPT09IFJFQUNUX0ZPUldBUkRfUkVGX1RZUEUgfHwgLy8gTm90ZTogTWVtbyBvbmx5IGNoZWNrcyBvdXRlciBwcm9wcyBoZXJlLlxuICAgIC8vIElubmVyIHByb3BzIGFyZSBjaGVja2VkIGluIHRoZSByZWNvbmNpbGVyLlxuICAgIHR5cGUuJCR0eXBlb2YgPT09IFJFQUNUX01FTU9fVFlQRSkpIHtcbiAgICAgIHByb3BUeXBlcyA9IHR5cGUucHJvcFR5cGVzO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgaWYgKHByb3BUeXBlcykge1xuICAgICAgLy8gSW50ZW50aW9uYWxseSBpbnNpZGUgdG8gYXZvaWQgdHJpZ2dlcmluZyBsYXp5IGluaXRpYWxpemVyczpcbiAgICAgIHZhciBuYW1lID0gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpO1xuICAgICAgY2hlY2tQcm9wVHlwZXMocHJvcFR5cGVzLCBlbGVtZW50LnByb3BzLCAncHJvcCcsIG5hbWUsIGVsZW1lbnQpO1xuICAgIH0gZWxzZSBpZiAodHlwZS5Qcm9wVHlwZXMgIT09IHVuZGVmaW5lZCAmJiAhcHJvcFR5cGVzTWlzc3BlbGxXYXJuaW5nU2hvd24pIHtcbiAgICAgIHByb3BUeXBlc01pc3NwZWxsV2FybmluZ1Nob3duID0gdHJ1ZTsgLy8gSW50ZW50aW9uYWxseSBpbnNpZGUgdG8gYXZvaWQgdHJpZ2dlcmluZyBsYXp5IGluaXRpYWxpemVyczpcblxuICAgICAgdmFyIF9uYW1lID0gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpO1xuXG4gICAgICBlcnJvcignQ29tcG9uZW50ICVzIGRlY2xhcmVkIGBQcm9wVHlwZXNgIGluc3RlYWQgb2YgYHByb3BUeXBlc2AuIERpZCB5b3UgbWlzc3BlbGwgdGhlIHByb3BlcnR5IGFzc2lnbm1lbnQ/JywgX25hbWUgfHwgJ1Vua25vd24nKTtcbiAgICB9XG5cbiAgICBpZiAodHlwZW9mIHR5cGUuZ2V0RGVmYXVsdFByb3BzID09PSAnZnVuY3Rpb24nICYmICF0eXBlLmdldERlZmF1bHRQcm9wcy5pc1JlYWN0Q2xhc3NBcHByb3ZlZCkge1xuICAgICAgZXJyb3IoJ2dldERlZmF1bHRQcm9wcyBpcyBvbmx5IHVzZWQgb24gY2xhc3NpYyBSZWFjdC5jcmVhdGVDbGFzcyAnICsgJ2RlZmluaXRpb25zLiBVc2UgYSBzdGF0aWMgcHJvcGVydHkgbmFtZWQgYGRlZmF1bHRQcm9wc2AgaW5zdGVhZC4nKTtcbiAgICB9XG4gIH1cbn1cbi8qKlxuICogR2l2ZW4gYSBmcmFnbWVudCwgdmFsaWRhdGUgdGhhdCBpdCBjYW4gb25seSBiZSBwcm92aWRlZCB3aXRoIGZyYWdtZW50IHByb3BzXG4gKiBAcGFyYW0ge1JlYWN0RWxlbWVudH0gZnJhZ21lbnRcbiAqL1xuXG5cbmZ1bmN0aW9uIHZhbGlkYXRlRnJhZ21lbnRQcm9wcyhmcmFnbWVudCkge1xuICB7XG4gICAgdmFyIGtleXMgPSBPYmplY3Qua2V5cyhmcmFnbWVudC5wcm9wcyk7XG5cbiAgICBmb3IgKHZhciBpID0gMDsgaSA8IGtleXMubGVuZ3RoOyBpKyspIHtcbiAgICAgIHZhciBrZXkgPSBrZXlzW2ldO1xuXG4gICAgICBpZiAoa2V5ICE9PSAnY2hpbGRyZW4nICYmIGtleSAhPT0gJ2tleScpIHtcbiAgICAgICAgc2V0Q3VycmVudGx5VmFsaWRhdGluZ0VsZW1lbnQoZnJhZ21lbnQpO1xuXG4gICAgICAgIGVycm9yKCdJbnZhbGlkIHByb3AgYCVzYCBzdXBwbGllZCB0byBgUmVhY3QuRnJhZ21lbnRgLiAnICsgJ1JlYWN0LkZyYWdtZW50IGNhbiBvbmx5IGhhdmUgYGtleWAgYW5kIGBjaGlsZHJlbmAgcHJvcHMuJywga2V5KTtcblxuICAgICAgICBzZXRDdXJyZW50bHlWYWxpZGF0aW5nRWxlbWVudChudWxsKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKGZyYWdtZW50LnJlZiAhPT0gbnVsbCkge1xuICAgICAgc2V0Q3VycmVudGx5VmFsaWRhdGluZ0VsZW1lbnQoZnJhZ21lbnQpO1xuXG4gICAgICBlcnJvcignSW52YWxpZCBhdHRyaWJ1dGUgYHJlZmAgc3VwcGxpZWQgdG8gYFJlYWN0LkZyYWdtZW50YC4nKTtcblxuICAgICAgc2V0Q3VycmVudGx5VmFsaWRhdGluZ0VsZW1lbnQobnVsbCk7XG4gICAgfVxuICB9XG59XG5cbnZhciBkaWRXYXJuQWJvdXRLZXlTcHJlYWQgPSB7fTtcbmZ1bmN0aW9uIGpzeFdpdGhWYWxpZGF0aW9uKHR5cGUsIHByb3BzLCBrZXksIGlzU3RhdGljQ2hpbGRyZW4sIHNvdXJjZSwgc2VsZikge1xuICB7XG4gICAgdmFyIHZhbGlkVHlwZSA9IGlzVmFsaWRFbGVtZW50VHlwZSh0eXBlKTsgLy8gV2Ugd2FybiBpbiB0aGlzIGNhc2UgYnV0IGRvbid0IHRocm93LiBXZSBleHBlY3QgdGhlIGVsZW1lbnQgY3JlYXRpb24gdG9cbiAgICAvLyBzdWNjZWVkIGFuZCB0aGVyZSB3aWxsIGxpa2VseSBiZSBlcnJvcnMgaW4gcmVuZGVyLlxuXG4gICAgaWYgKCF2YWxpZFR5cGUpIHtcbiAgICAgIHZhciBpbmZvID0gJyc7XG5cbiAgICAgIGlmICh0eXBlID09PSB1bmRlZmluZWQgfHwgdHlwZW9mIHR5cGUgPT09ICdvYmplY3QnICYmIHR5cGUgIT09IG51bGwgJiYgT2JqZWN0LmtleXModHlwZSkubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIGluZm8gKz0gJyBZb3UgbGlrZWx5IGZvcmdvdCB0byBleHBvcnQgeW91ciBjb21wb25lbnQgZnJvbSB0aGUgZmlsZSAnICsgXCJpdCdzIGRlZmluZWQgaW4sIG9yIHlvdSBtaWdodCBoYXZlIG1peGVkIHVwIGRlZmF1bHQgYW5kIG5hbWVkIGltcG9ydHMuXCI7XG4gICAgICB9XG5cbiAgICAgIHZhciBzb3VyY2VJbmZvID0gZ2V0U291cmNlSW5mb0Vycm9yQWRkZW5kdW0oc291cmNlKTtcblxuICAgICAgaWYgKHNvdXJjZUluZm8pIHtcbiAgICAgICAgaW5mbyArPSBzb3VyY2VJbmZvO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgaW5mbyArPSBnZXREZWNsYXJhdGlvbkVycm9yQWRkZW5kdW0oKTtcbiAgICAgIH1cblxuICAgICAgdmFyIHR5cGVTdHJpbmc7XG5cbiAgICAgIGlmICh0eXBlID09PSBudWxsKSB7XG4gICAgICAgIHR5cGVTdHJpbmcgPSAnbnVsbCc7XG4gICAgICB9IGVsc2UgaWYgKGlzQXJyYXkodHlwZSkpIHtcbiAgICAgICAgdHlwZVN0cmluZyA9ICdhcnJheSc7XG4gICAgICB9IGVsc2UgaWYgKHR5cGUgIT09IHVuZGVmaW5lZCAmJiB0eXBlLiQkdHlwZW9mID09PSBSRUFDVF9FTEVNRU5UX1RZUEUpIHtcbiAgICAgICAgdHlwZVN0cmluZyA9IFwiPFwiICsgKGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlLnR5cGUpIHx8ICdVbmtub3duJykgKyBcIiAvPlwiO1xuICAgICAgICBpbmZvID0gJyBEaWQgeW91IGFjY2lkZW50YWxseSBleHBvcnQgYSBKU1ggbGl0ZXJhbCBpbnN0ZWFkIG9mIGEgY29tcG9uZW50Pyc7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0eXBlU3RyaW5nID0gdHlwZW9mIHR5cGU7XG4gICAgICB9XG5cbiAgICAgIGVycm9yKCdSZWFjdC5qc3g6IHR5cGUgaXMgaW52YWxpZCAtLSBleHBlY3RlZCBhIHN0cmluZyAoZm9yICcgKyAnYnVpbHQtaW4gY29tcG9uZW50cykgb3IgYSBjbGFzcy9mdW5jdGlvbiAoZm9yIGNvbXBvc2l0ZSAnICsgJ2NvbXBvbmVudHMpIGJ1dCBnb3Q6ICVzLiVzJywgdHlwZVN0cmluZywgaW5mbyk7XG4gICAgfVxuXG4gICAgdmFyIGVsZW1lbnQgPSBqc3hERVYkMSh0eXBlLCBwcm9wcywga2V5LCBzb3VyY2UsIHNlbGYpOyAvLyBUaGUgcmVzdWx0IGNhbiBiZSBudWxsaXNoIGlmIGEgbW9jayBvciBhIGN1c3RvbSBmdW5jdGlvbiBpcyB1c2VkLlxuICAgIC8vIFRPRE86IERyb3AgdGhpcyB3aGVuIHRoZXNlIGFyZSBubyBsb25nZXIgYWxsb3dlZCBhcyB0aGUgdHlwZSBhcmd1bWVudC5cblxuICAgIGlmIChlbGVtZW50ID09IG51bGwpIHtcbiAgICAgIHJldHVybiBlbGVtZW50O1xuICAgIH0gLy8gU2tpcCBrZXkgd2FybmluZyBpZiB0aGUgdHlwZSBpc24ndCB2YWxpZCBzaW5jZSBvdXIga2V5IHZhbGlkYXRpb24gbG9naWNcbiAgICAvLyBkb2Vzbid0IGV4cGVjdCBhIG5vbi1zdHJpbmcvZnVuY3Rpb24gdHlwZSBhbmQgY2FuIHRocm93IGNvbmZ1c2luZyBlcnJvcnMuXG4gICAgLy8gV2UgZG9uJ3Qgd2FudCBleGNlcHRpb24gYmVoYXZpb3IgdG8gZGlmZmVyIGJldHdlZW4gZGV2IGFuZCBwcm9kLlxuICAgIC8vIChSZW5kZXJpbmcgd2lsbCB0aHJvdyB3aXRoIGEgaGVscGZ1bCBtZXNzYWdlIGFuZCBhcyBzb29uIGFzIHRoZSB0eXBlIGlzXG4gICAgLy8gZml4ZWQsIHRoZSBrZXkgd2FybmluZ3Mgd2lsbCBhcHBlYXIuKVxuXG5cbiAgICBpZiAodmFsaWRUeXBlKSB7XG4gICAgICB2YXIgY2hpbGRyZW4gPSBwcm9wcy5jaGlsZHJlbjtcblxuICAgICAgaWYgKGNoaWxkcmVuICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgaWYgKGlzU3RhdGljQ2hpbGRyZW4pIHtcbiAgICAgICAgICBpZiAoaXNBcnJheShjaGlsZHJlbikpIHtcbiAgICAgICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgY2hpbGRyZW4ubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgICAgdmFsaWRhdGVDaGlsZEtleXMoY2hpbGRyZW5baV0sIHR5cGUpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpZiAoT2JqZWN0LmZyZWV6ZSkge1xuICAgICAgICAgICAgICBPYmplY3QuZnJlZXplKGNoaWxkcmVuKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgZXJyb3IoJ1JlYWN0LmpzeDogU3RhdGljIGNoaWxkcmVuIHNob3VsZCBhbHdheXMgYmUgYW4gYXJyYXkuICcgKyAnWW91IGFyZSBsaWtlbHkgZXhwbGljaXRseSBjYWxsaW5nIFJlYWN0LmpzeHMgb3IgUmVhY3QuanN4REVWLiAnICsgJ1VzZSB0aGUgQmFiZWwgdHJhbnNmb3JtIGluc3RlYWQuJyk7XG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHZhbGlkYXRlQ2hpbGRLZXlzKGNoaWxkcmVuLCB0eXBlKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIGlmIChoYXNPd25Qcm9wZXJ0eS5jYWxsKHByb3BzLCAna2V5JykpIHtcbiAgICAgIHZhciBjb21wb25lbnROYW1lID0gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpO1xuICAgICAgdmFyIGtleXMgPSBPYmplY3Qua2V5cyhwcm9wcykuZmlsdGVyKGZ1bmN0aW9uIChrKSB7XG4gICAgICAgIHJldHVybiBrICE9PSAna2V5JztcbiAgICAgIH0pO1xuICAgICAgdmFyIGJlZm9yZUV4YW1wbGUgPSBrZXlzLmxlbmd0aCA+IDAgPyAne2tleTogc29tZUtleSwgJyArIGtleXMuam9pbignOiAuLi4sICcpICsgJzogLi4ufScgOiAne2tleTogc29tZUtleX0nO1xuXG4gICAgICBpZiAoIWRpZFdhcm5BYm91dEtleVNwcmVhZFtjb21wb25lbnROYW1lICsgYmVmb3JlRXhhbXBsZV0pIHtcbiAgICAgICAgdmFyIGFmdGVyRXhhbXBsZSA9IGtleXMubGVuZ3RoID4gMCA/ICd7JyArIGtleXMuam9pbignOiAuLi4sICcpICsgJzogLi4ufScgOiAne30nO1xuXG4gICAgICAgIGVycm9yKCdBIHByb3BzIG9iamVjdCBjb250YWluaW5nIGEgXCJrZXlcIiBwcm9wIGlzIGJlaW5nIHNwcmVhZCBpbnRvIEpTWDpcXG4nICsgJyAgbGV0IHByb3BzID0gJXM7XFxuJyArICcgIDwlcyB7Li4ucHJvcHN9IC8+XFxuJyArICdSZWFjdCBrZXlzIG11c3QgYmUgcGFzc2VkIGRpcmVjdGx5IHRvIEpTWCB3aXRob3V0IHVzaW5nIHNwcmVhZDpcXG4nICsgJyAgbGV0IHByb3BzID0gJXM7XFxuJyArICcgIDwlcyBrZXk9e3NvbWVLZXl9IHsuLi5wcm9wc30gLz4nLCBiZWZvcmVFeGFtcGxlLCBjb21wb25lbnROYW1lLCBhZnRlckV4YW1wbGUsIGNvbXBvbmVudE5hbWUpO1xuXG4gICAgICAgIGRpZFdhcm5BYm91dEtleVNwcmVhZFtjb21wb25lbnROYW1lICsgYmVmb3JlRXhhbXBsZV0gPSB0cnVlO1xuICAgICAgfVxuICAgIH1cblxuICAgIGlmICh0eXBlID09PSBSRUFDVF9GUkFHTUVOVF9UWVBFKSB7XG4gICAgICB2YWxpZGF0ZUZyYWdtZW50UHJvcHMoZWxlbWVudCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHZhbGlkYXRlUHJvcFR5cGVzKGVsZW1lbnQpO1xuICAgIH1cblxuICAgIHJldHVybiBlbGVtZW50O1xuICB9XG59IC8vIFRoZXNlIHR3byBmdW5jdGlvbnMgZXhpc3QgdG8gc3RpbGwgZ2V0IGNoaWxkIHdhcm5pbmdzIGluIGRldlxuXG52YXIganN4REVWID0ganN4V2l0aFZhbGlkYXRpb24gO1xuXG5leHBvcnRzLkZyYWdtZW50ID0gUkVBQ1RfRlJBR01FTlRfVFlQRTtcbmV4cG9ydHMuanN4REVWID0ganN4REVWO1xuICB9KSgpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/OWNjMSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/navigation.js":
/*!*****************************************!*\
  !*** ./node_modules/next/navigation.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6IkFBQUEsK0pBQStEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L25hdmlnYXRpb24uanM/MzAyYSJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGlzdC9jbGllbnQvY29tcG9uZW50cy9uYXZpZ2F0aW9uJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/navigation.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Capp%5Cstrategies%5Cpage.tsx&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);