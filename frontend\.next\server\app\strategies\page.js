/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/strategies/page";
exports.ids = ["app/strategies/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fstrategies%2Fpage&page=%2Fstrategies%2Fpage&appPaths=%2Fstrategies%2Fpage&pagePath=private-next-app-dir%2Fstrategies%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fstrategies%2Fpage&page=%2Fstrategies%2Fpage&appPaths=%2Fstrategies%2Fpage&pagePath=private-next-app-dir%2Fstrategies%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'strategies',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/strategies/page.tsx */ \"(rsc)/./src/app/strategies/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/strategies/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/strategies/page\",\n        pathname: \"/strategies\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fstrategies%2Fpage&page=%2Fstrategies%2Fpage&appPaths=%2Fstrategies%2Fpage&pagePath=private-next-app-dir%2Fstrategies%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22variable%22%3A%22--font-inter%22%2C%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Ccomponents%5CConditionalLayout.tsx&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Ccomponents%5CRouteGuard.tsx&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Ccontexts%5CAuthContext.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22variable%22%3A%22--font-inter%22%2C%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Ccomponents%5CConditionalLayout.tsx&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Ccomponents%5CRouteGuard.tsx&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Ccontexts%5CAuthContext.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ConditionalLayout.tsx */ \"(ssr)/./src/components/ConditionalLayout.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/RouteGuard.tsx */ \"(ssr)/./src/components/RouteGuard.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDbWFoZXNoJTVDRG9jdW1lbnRzJTVDTml2ZXNodG9yJTVDZnJvbnRlbmQlNUNzcmMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q21haGVzaCU1Q0RvY3VtZW50cyU1Q05pdmVzaHRvciU1Q2Zyb250ZW5kJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnZhcmlhYmxlJTIyJTNBJTIyLS1mb250LWludGVyJTIyJTJDJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNtYWhlc2glNUNEb2N1bWVudHMlNUNOaXZlc2h0b3IlNUNmcm9udGVuZCU1Q3NyYyU1Q2NvbXBvbmVudHMlNUNDb25kaXRpb25hbExheW91dC50c3gmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNtYWhlc2glNUNEb2N1bWVudHMlNUNOaXZlc2h0b3IlNUNmcm9udGVuZCU1Q3NyYyU1Q2NvbXBvbmVudHMlNUNSb3V0ZUd1YXJkLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q21haGVzaCU1Q0RvY3VtZW50cyU1Q05pdmVzaHRvciU1Q2Zyb250ZW5kJTVDc3JjJTVDY29udGV4dHMlNUNBdXRoQ29udGV4dC50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdMQUE4SDtBQUM5SCwwS0FBdUg7QUFDdkgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uaXZlc2h0b3ItZnJvbnRlbmQvPzYyY2EiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxtYWhlc2hcXFxcRG9jdW1lbnRzXFxcXE5pdmVzaHRvclxcXFxmcm9udGVuZFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxDb25kaXRpb25hbExheW91dC50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG1haGVzaFxcXFxEb2N1bWVudHNcXFxcTml2ZXNodG9yXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXFJvdXRlR3VhcmQudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxtYWhlc2hcXFxcRG9jdW1lbnRzXFxcXE5pdmVzaHRvclxcXFxmcm9udGVuZFxcXFxzcmNcXFxcY29udGV4dHNcXFxcQXV0aENvbnRleHQudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22variable%22%3A%22--font-inter%22%2C%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Ccomponents%5CConditionalLayout.tsx&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Ccomponents%5CRouteGuard.tsx&modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Ccontexts%5CAuthContext.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Capp%5Cstrategies%5Cpage.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Capp%5Cstrategies%5Cpage.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/strategies/page.tsx */ \"(ssr)/./src/app/strategies/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDbWFoZXNoJTVDRG9jdW1lbnRzJTVDTml2ZXNodG9yJTVDZnJvbnRlbmQlNUNzcmMlNUNhcHAlNUNzdHJhdGVnaWVzJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbml2ZXNodG9yLWZyb250ZW5kLz9kYjNjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbWFoZXNoXFxcXERvY3VtZW50c1xcXFxOaXZlc2h0b3JcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxzdHJhdGVnaWVzXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Capp%5Cstrategies%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/strategies/page.tsx":
/*!*************************************!*\
  !*** ./src/app/strategies/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst StrategiesPage = ()=>{\n    const { isAuthenticated, user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const [activeStrategy, setActiveStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"weekly-high\");\n    // Weekly High Strategy state\n    const [weeklyHighSignals, setWeeklyHighSignals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [bohStocks, setBohStocks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expandedRows, setExpandedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [processingStatus, setProcessingStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const strategies = [\n        {\n            id: \"weekly-high\",\n            name: \"Weekly High\",\n            description: \"Breakout strategy based on weekly high levels\",\n            status: \"Active\",\n            performance: \"+12.5%\",\n            trades: 45,\n            winRate: \"68%\"\n        },\n        {\n            id: \"rsi\",\n            name: \"RSI Mean Reversion\",\n            description: \"Mean reversion strategy using RSI indicator\",\n            status: \"Active\",\n            performance: \"+8.3%\",\n            trades: 32,\n            winRate: \"72%\"\n        },\n        {\n            id: \"consolidated-stock\",\n            name: \"Consolidated Stock\",\n            description: \"Breakout from consolidation patterns\",\n            status: \"Paused\",\n            performance: \"+5.1%\",\n            trades: 18,\n            winRate: \"61%\"\n        }\n    ];\n    const tabs = [\n        {\n            id: \"signal\",\n            name: \"Signal\",\n            emoji: \"\\uD83D\\uDCCA\"\n        },\n        {\n            id: \"gtt-order\",\n            name: \"GTT Order\",\n            emoji: \"⚡\"\n        },\n        {\n            id: \"current-holdings\",\n            name: \"Current Holdings\",\n            emoji: \"\\uD83D\\uDCBC\"\n        }\n    ];\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"signal\");\n    // Handle URL parameters for strategy selection\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const strategy = searchParams.get(\"strategy\");\n        if (strategy && [\n            \"weekly-high\",\n            \"rsi\",\n            \"consolidated-stock\"\n        ].includes(strategy)) {\n            setActiveStrategy(strategy);\n        }\n    }, [\n        searchParams\n    ]);\n    // Weekly High Strategy Functions\n    const fetchWeeklyHighSignals = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Always try to get a fresh token to avoid 401 errors\n            console.log(\"\\uD83D\\uDD10 Getting fresh authentication token for Weekly High Strategy...\");\n            console.log(\"\\uD83C\\uDF10 Backend server URL: http://localhost:8000\");\n            setProcessingStatus(\"Authenticating with demo credentials...\");\n            const loginResponse = await fetch(\"http://localhost:8000/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email: \"<EMAIL>\",\n                    password: \"demo123\"\n                })\n            });\n            console.log(\"\\uD83D\\uDCE1 Login response status:\", loginResponse.status, loginResponse.statusText);\n            if (!loginResponse.ok) {\n                const errorText = await loginResponse.text();\n                console.error(\"❌ Weekly High Strategy login failed:\", loginResponse.status, errorText);\n                throw new Error(`Authentication failed: ${loginResponse.status} ${loginResponse.statusText}. Error: ${errorText}`);\n            }\n            const loginData = await loginResponse.json();\n            const token = loginData.access_token;\n            localStorage.setItem(\"auth_token\", token);\n            console.log(\"✅ Fresh token obtained for Weekly High Strategy\");\n            // Step 1: Fetch BOH eligible stocks from backend API with extended timeout\n            console.log(\"\\uD83D\\uDD0D Fetching BOH eligible stocks from backend...\");\n            setProcessingStatus(\"Fetching BOH eligible stocks from backend...\");\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>{\n                console.log(\"⏰ BOH API request timed out after 60 seconds\");\n                controller.abort();\n            }, 60000); // Extended to 60 second timeout for BOH API\n            let eligibleStocks = [];\n            try {\n                console.log(\"\\uD83C\\uDF10 Making BOH API request with token:\", token ? token.substring(0, 20) + \"...\" : \"No token\");\n                const bohResponse = await fetch(\"http://localhost:8000/api/boh/eligible\", {\n                    method: \"GET\",\n                    headers: {\n                        \"Authorization\": `Bearer ${token}`,\n                        \"Content-Type\": \"application/json\"\n                    },\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                console.log(\"\\uD83D\\uDCE1 BOH API response status:\", bohResponse.status, bohResponse.statusText);\n                if (!bohResponse.ok) {\n                    const errorText = await bohResponse.text();\n                    console.error(\"❌ BOH API error response:\", errorText);\n                    throw new Error(`BOH API returned ${bohResponse.status}: ${bohResponse.statusText}`);\n                }\n                const bohData = await bohResponse.json();\n                console.log(\"\\uD83D\\uDCCA BOH API data received:\", {\n                    totalStocks: bohData.stocks?.length || 0,\n                    eligibleCount: bohData.eligible_count || 0\n                });\n                // Filter only BOH eligible stocks (boh_eligible = \"Yes\")\n                eligibleStocks = bohData.stocks.filter((stock)=>stock.is_eligible && stock.boh_eligible === \"Yes\");\n                setBohStocks(eligibleStocks);\n                console.log(`✅ Found ${eligibleStocks.length} BOH eligible stocks from backend`);\n                if (eligibleStocks.length === 0) {\n                    setWeeklyHighSignals([]);\n                    setLastUpdated(new Date());\n                    setProcessingStatus(\"\");\n                    console.log(\"⚠️ No BOH eligible stocks found\");\n                    return;\n                }\n            } catch (error) {\n                clearTimeout(timeoutId);\n                console.error(\"❌ Failed to fetch BOH eligible stocks:\", error);\n                // Provide more specific error messages\n                if (error.name === \"AbortError\") {\n                    throw new Error(\"BOH API request timed out. The backend may be processing a large number of stocks. Please try again.\");\n                } else if (error.message?.includes(\"Failed to fetch\")) {\n                    throw new Error(\"Unable to connect to the backend server. Please check if the server is running.\");\n                } else {\n                    throw new Error(`Failed to fetch BOH eligible stocks: ${error.message}`);\n                }\n            }\n            // Step 2: Calculate Weekly High signals with real Yahoo Finance data\n            console.log(\"\\uD83D\\uDCC8 Calculating Weekly High signals with Yahoo Finance data...\");\n            setProcessingStatus(`Processing ${eligibleStocks.length} BOH eligible stocks for Weekly High signals...`);\n            const signals = await calculateWeeklyHighSignals(eligibleStocks, []);\n            setWeeklyHighSignals(signals);\n            setLastUpdated(new Date());\n            setProcessingStatus(\"\");\n            console.log(`🎯 Generated ${signals.length} Weekly High signals from ${eligibleStocks.length} BOH eligible stocks`);\n        } catch (error) {\n            console.error(\"Error in fetchWeeklyHighSignals:\", error);\n            setError(error instanceof Error ? error.message : \"Unknown error occurred\");\n            setProcessingStatus(\"\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const calculateWeeklyHighSignals = async (eligibleStocks, holdings)=>{\n        const signals = [];\n        const holdingSymbols = new Set(holdings.map((h)=>h.symbol));\n        let successCount = 0;\n        let failureCount = 0;\n        console.log(`📊 Processing ${eligibleStocks.length} BOH eligible stocks for Weekly High signals...`);\n        for(let i = 0; i < eligibleStocks.length; i++){\n            const stock = eligibleStocks[i];\n            try {\n                console.log(`🔍 Processing ${i + 1}/${eligibleStocks.length}: ${stock.stock_name}...`);\n                setProcessingStatus(`Processing ${i + 1}/${eligibleStocks.length}: ${stock.stock_name} (✅${successCount} ❌${failureCount})`);\n                // Check if already purchased\n                const alreadyPurchased = holdingSymbols.has(stock.stock_name);\n                // Try multiple approaches to get OHLC data\n                let weeklyOHLCData = [];\n                let lastWeekHighestPrice = 0;\n                let dataSource = \"yahoo_finance\";\n                try {\n                    // First attempt: Fetch real OHLC data from Yahoo Finance\n                    weeklyOHLCData = await fetchYahooFinanceOHLC(stock.stock_name);\n                    lastWeekHighestPrice = getLastWeekHighestPrice(weeklyOHLCData);\n                    dataSource = \"yahoo_finance\";\n                } catch (yahooError) {\n                    console.log(`⚠️ Yahoo Finance failed for ${stock.stock_name}, marking as data unavailable`);\n                    // No fallback calculation - mark as unavailable\n                    dataSource = \"unavailable\";\n                    lastWeekHighestPrice = 0;\n                    weeklyOHLCData = [];\n                }\n                // Calculate suggested buy price and other metrics\n                const suggestedBuyPrice = dataSource === \"yahoo_finance\" ? lastWeekHighestPrice + 0.05 : 0;\n                const percentageDifference = dataSource === \"yahoo_finance\" ? (stock.cmp - suggestedBuyPrice) / suggestedBuyPrice * 100 : 0;\n                const suggestedGTTQuantity = dataSource === \"yahoo_finance\" ? Math.floor(2000 / suggestedBuyPrice) : 0;\n                const signal = {\n                    stock_name: stock.stock_name,\n                    cmp: stock.cmp,\n                    already_purchased: alreadyPurchased,\n                    last_week_highest_price: lastWeekHighestPrice,\n                    suggested_buy_price: suggestedBuyPrice,\n                    percentage_difference: percentageDifference,\n                    suggested_gtt_quantity: suggestedGTTQuantity,\n                    weekly_ohlc_data: weeklyOHLCData,\n                    data_source: dataSource\n                };\n                signals.push(signal);\n                if (dataSource === \"yahoo_finance\") {\n                    successCount++;\n                    console.log(`✅ ${stock.stock_name}: Weekly High = ₹${lastWeekHighestPrice.toFixed(2)}, Buy Price = ₹${suggestedBuyPrice.toFixed(2)}, Diff = ${percentageDifference.toFixed(2)}%`);\n                } else {\n                    failureCount++;\n                    console.log(`❌ ${stock.stock_name}: Data unavailable`);\n                }\n                // Add delay to avoid overwhelming the API\n                if (i < eligibleStocks.length - 1) {\n                    await new Promise((resolve)=>setTimeout(resolve, 100));\n                }\n            } catch (error) {\n                failureCount++;\n                console.error(`❌ Error processing ${stock.stock_name}:`, error);\n                // Add signal with unavailable data\n                const signal = {\n                    stock_name: stock.stock_name,\n                    cmp: stock.cmp,\n                    already_purchased: holdingSymbols.has(stock.stock_name),\n                    last_week_highest_price: 0,\n                    suggested_buy_price: 0,\n                    percentage_difference: 0,\n                    suggested_gtt_quantity: 0,\n                    weekly_ohlc_data: [],\n                    data_source: \"unavailable\"\n                };\n                signals.push(signal);\n            }\n        }\n        console.log(`🎯 Successfully generated ${signals.length} Weekly High signals from ${eligibleStocks.length} BOH eligible stocks (✅${successCount} ❌${failureCount})`);\n        setProcessingStatus(\"\");\n        return signals;\n    };\n    const fetchYahooFinanceOHLC = async (stockSymbol)=>{\n        // Try multiple approaches to get data\n        const attempts = [\n            // Attempt 1: Current week data\n            ()=>fetchYahooDataWithDates(stockSymbol, 0),\n            // Attempt 2: Previous week data\n            ()=>fetchYahooDataWithDates(stockSymbol, 1),\n            // Attempt 3: Two weeks ago data\n            ()=>fetchYahooDataWithDates(stockSymbol, 2)\n        ];\n        for(let i = 0; i < attempts.length; i++){\n            try {\n                console.log(`📊 Attempt ${i + 1}/3: Fetching OHLC data for ${stockSymbol}...`);\n                const data = await attempts[i]();\n                if (data && data.length > 0) {\n                    console.log(`✅ Successfully fetched ${data.length} days of OHLC data for ${stockSymbol} (attempt ${i + 1})`);\n                    return data;\n                }\n            } catch (error) {\n                console.log(`⚠️ Attempt ${i + 1} failed for ${stockSymbol}:`, error);\n                if (i === attempts.length - 1) {\n                    throw error;\n                }\n            }\n        }\n        throw new Error(`Failed to fetch OHLC data for ${stockSymbol} after ${attempts.length} attempts`);\n    };\n    const fetchYahooDataWithDates = async (stockSymbol, weeksBack)=>{\n        const now = new Date();\n        const startDate = new Date(now);\n        startDate.setDate(now.getDate() - 7 * (weeksBack + 1)); // Go back weeksBack+1 weeks\n        const endDate = new Date(now);\n        endDate.setDate(now.getDate() - 7 * weeksBack); // End weeksBack weeks ago\n        const formatDate = (date)=>{\n            return date.toISOString().split(\"T\")[0];\n        };\n        const startDateStr = formatDate(startDate);\n        const endDateStr = formatDate(endDate);\n        console.log(`📅 Fetching ${stockSymbol} data from ${startDateStr} to ${endDateStr} (${weeksBack} weeks back)`);\n        const corsProxy = \"https://cors-anywhere.herokuapp.com/\";\n        const yahooUrl = `${corsProxy}https://query1.finance.yahoo.com/v7/finance/download/${stockSymbol}.NS?period1=${Math.floor(startDate.getTime() / 1000)}&period2=${Math.floor(endDate.getTime() / 1000)}&interval=1d&events=history`;\n        const response = await fetch(yahooUrl, {\n            headers: {\n                \"X-Requested-With\": \"XMLHttpRequest\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`Yahoo Finance API error: ${response.status} ${response.statusText}`);\n        }\n        const csvText = await response.text();\n        const lines = csvText.trim().split(\"\\n\");\n        if (lines.length <= 1) {\n            throw new Error(\"No data available from Yahoo Finance\");\n        }\n        const ohlcData = [];\n        // Skip header row\n        for(let i = 1; i < lines.length; i++){\n            const columns = lines[i].split(\",\");\n            if (columns.length >= 6) {\n                const date = columns[0];\n                const open = parseFloat(columns[1]);\n                const high = parseFloat(columns[2]);\n                const low = parseFloat(columns[3]);\n                const close = parseFloat(columns[4]);\n                if (!isNaN(open) && !isNaN(high) && !isNaN(low) && !isNaN(close)) {\n                    const dayOfWeek = new Date(date).toLocaleDateString(\"en-US\", {\n                        weekday: \"long\"\n                    });\n                    ohlcData.push({\n                        date,\n                        day: dayOfWeek,\n                        open: Math.round(open * 100) / 100,\n                        high: Math.round(high * 100) / 100,\n                        low: Math.round(low * 100) / 100,\n                        close: Math.round(close * 100) / 100\n                    });\n                }\n            }\n        }\n        if (ohlcData.length === 0) {\n            throw new Error(\"No valid OHLC data found\");\n        }\n        return ohlcData;\n    };\n    const getLastWeekHighestPrice = (ohlcData)=>{\n        if (ohlcData.length === 0) return 0;\n        // Find the maximum 'High' value from all days\n        const weeklyHigh = Math.max(...ohlcData.map((day)=>day.high));\n        return Math.round(weeklyHigh * 100) / 100;\n    };\n    const toggleRowExpansion = (stockName)=>{\n        const newExpandedRows = new Set(expandedRows);\n        if (newExpandedRows.has(stockName)) {\n            newExpandedRows.delete(stockName);\n        } else {\n            newExpandedRows.add(stockName);\n        }\n        setExpandedRows(newExpandedRows);\n    };\n    // Load data when strategy changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (activeStrategy === \"weekly-high\") {\n            // Always try to fetch signals, the function handles authentication internally\n            fetchWeeklyHighSignals();\n        }\n    }, [\n        activeStrategy\n    ]);\n    const renderWeeklyHighSignalTab = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent\",\n                                        children: \"\\uD83D\\uDCC8 Weekly High Strategy - Signal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: \"Breakout opportunities from BOH eligible stocks with strong weekly price structure\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: fetchWeeklyHighSignals,\n                                disabled: loading,\n                                className: \"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"Processing...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: \"\\uD83D\\uDD04 Refresh Signals\"\n                                }, void 0, false)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 11\n                    }, undefined),\n                    processingStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-800 text-sm font-medium\",\n                                    children: processingStatus\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                            lineNumber: 486,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 485,\n                        columnNumber: 13\n                    }, undefined),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-600 text-2xl mr-3\",\n                                            children: \"❌\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-red-800 font-medium\",\n                                                    children: \"Error\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-600 text-sm\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setError(null);\n                                        fetchWeeklyHighSignals();\n                                    },\n                                    className: \"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n                                    children: \"Retry\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 13\n                    }, undefined),\n                    lastUpdated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: \"Last Updated:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 15\n                            }, undefined),\n                            \" \",\n                            lastUpdated.toLocaleString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 13\n                    }, undefined),\n                    weeklyHighSignals.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"CMP\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Weekly High\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Data Source\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Buy Price\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"% Diff\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"GTT Qty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"OHLC Data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white divide-y divide-gray-200\",\n                                    children: weeklyHighSignals.map((signal, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: index % 2 === 0 ? \"bg-white\" : \"bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: signal.stock_name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    signal.cmp.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: signal.data_source === \"unavailable\" ? \"Data Not Available\" : `₹${signal.last_week_highest_price.toFixed(2)}`\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${signal.data_source === \"yahoo_finance\" ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"}`,\n                                                                children: signal.data_source === \"yahoo_finance\" ? \"\\uD83D\\uDCCA Real Data\" : \"❌ Unavailable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-blue-600\",\n                                                                children: signal.data_source === \"unavailable\" ? \"N/A\" : `₹${signal.suggested_buy_price.toFixed(2)}`\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `text-sm font-medium ${signal.data_source === \"unavailable\" ? \"text-gray-500\" : signal.percentage_difference >= 0 ? \"text-green-600\" : \"text-red-600\"}`,\n                                                                children: signal.data_source === \"unavailable\" ? \"N/A\" : `${signal.percentage_difference >= 0 ? \"+\" : \"\"}${signal.percentage_difference.toFixed(2)}%`\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: signal.data_source === \"unavailable\" ? \"N/A\" : signal.suggested_gtt_quantity\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>toggleRowExpansion(signal.stock_name),\n                                                                className: \"inline-flex items-center px-3 py-1 border border-gray-300 text-xs leading-4 font-medium rounded text-gray-700 bg-white hover:bg-gray-50\",\n                                                                children: expandedRows.has(signal.stock_name) ? \"\\uD83D\\uDCCA Hide OHLC\" : \"\\uD83D\\uDCCA Show OHLC\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                expandedRows.has(signal.stock_name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 8,\n                                                        className: \"px-6 py-4 bg-gray-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: [\n                                                                        \"\\uD83D\\uDCC8 Weekly OHLC Data for \",\n                                                                        signal.stock_name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                signal.data_source === \"unavailable\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center py-8\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-red-500 text-lg mb-2\",\n                                                                            children: \"❌\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                            lineNumber: 603,\n                                                                            columnNumber: 35\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Yahoo Finance data could not be retrieved for this stock\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 35\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                                            children: \"No OHLC data available for signal calculation\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                            lineNumber: 605,\n                                                                            columnNumber: 35\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 33\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"overflow-x-auto\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                                        className: \"min-w-full divide-y divide-gray-200\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                                                className: \"bg-gray-100\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                            className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\",\n                                                                                            children: \"Day\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                            lineNumber: 612,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                            className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\",\n                                                                                            children: \"Date\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                            lineNumber: 613,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                            className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\",\n                                                                                            children: \"Open\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                            lineNumber: 614,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                            className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\",\n                                                                                            children: \"High\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                            lineNumber: 615,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                            className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\",\n                                                                                            children: \"Low\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                            lineNumber: 616,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                            className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\",\n                                                                                            children: \"Close\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                            lineNumber: 617,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                                            className: \"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\",\n                                                                                            children: \"Weekly High\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                            lineNumber: 618,\n                                                                                            columnNumber: 41\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                    lineNumber: 611,\n                                                                                    columnNumber: 39\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                lineNumber: 610,\n                                                                                columnNumber: 37\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                                                className: \"bg-white divide-y divide-gray-200\",\n                                                                                children: signal.weekly_ohlc_data.map((ohlc, ohlcIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                                                                children: ohlc.day\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                                lineNumber: 624,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                                                                children: ohlc.date\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                                lineNumber: 625,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                                                                children: [\n                                                                                                    \"₹\",\n                                                                                                    ohlc.open.toFixed(2)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                                lineNumber: 626,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                                className: \"px-4 py-2 text-sm font-medium text-green-600\",\n                                                                                                children: [\n                                                                                                    \"₹\",\n                                                                                                    ohlc.high.toFixed(2)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                                lineNumber: 627,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                                                                children: [\n                                                                                                    \"₹\",\n                                                                                                    ohlc.low.toFixed(2)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                                lineNumber: 628,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                                className: \"px-4 py-2 text-sm text-gray-900\",\n                                                                                                children: [\n                                                                                                    \"₹\",\n                                                                                                    ohlc.close.toFixed(2)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                                lineNumber: 629,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                                className: \"px-4 py-2 text-sm font-bold text-blue-600\",\n                                                                                                children: ohlc.high === signal.last_week_highest_price ? `₹${signal.last_week_highest_price.toFixed(2)}` : \"\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                                lineNumber: 630,\n                                                                                                columnNumber: 43\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, ohlcIndex, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                        lineNumber: 623,\n                                                                                        columnNumber: 41\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                lineNumber: 621,\n                                                                                columnNumber: 37\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                        lineNumber: 609,\n                                                                        columnNumber: 35\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 33\n                                                                }, undefined),\n                                                                signal.data_source === \"yahoo_finance\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Calculation:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                    lineNumber: 641,\n                                                                                    columnNumber: 38\n                                                                                }, undefined),\n                                                                                \" Weekly High = Maximum of all 'High' values = ₹\",\n                                                                                signal.last_week_highest_price.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                            lineNumber: 641,\n                                                                            columnNumber: 35\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Suggested Buy Price:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                                    lineNumber: 642,\n                                                                                    columnNumber: 38\n                                                                                }, undefined),\n                                                                                \" ₹\",\n                                                                                signal.last_week_highest_price.toFixed(2),\n                                                                                \" + ₹0.05 = ₹\",\n                                                                                signal.suggested_buy_price.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                            lineNumber: 642,\n                                                                            columnNumber: 35\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                                    lineNumber: 640,\n                                                                    columnNumber: 33\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, signal.stock_name, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-6xl mb-4\",\n                                children: \"\\uD83D\\uDCCA\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 656,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No Weekly High signals available\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 657,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: loading ? \"Loading signals...\" : \"Ensure BOH eligible stocks are available and try refreshing.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 658,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                lineNumber: 455,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n            lineNumber: 453,\n            columnNumber: 7\n        }, undefined);\n    };\n    const renderTabContent = ()=>{\n        const strategy = strategies.find((s)=>s.id === activeStrategy);\n        switch(activeTab){\n            case \"signal\":\n                if (activeStrategy === \"weekly-high\") {\n                    return renderWeeklyHighSignalTab();\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: [\n                                    \"\\uD83D\\uDCC8 Current Signals - \",\n                                    strategy?.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 679,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"RELIANCE\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Signal: BUY | Price: ₹2,450\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 687,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                            children: \"Strong Buy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 690,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: \"Confidence: 85%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 684,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"TCS\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Signal: BUY | Price: ₹3,680\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                            children: \"Buy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: \"Confidence: 72%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                    lineNumber: 677,\n                    columnNumber: 11\n                }, undefined);\n            case \"gtt-order\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: [\n                                    \"⚡ GTT Orders - \",\n                                    strategy?.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 720,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"HDFC\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 727,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"GTT Buy: ₹1,580 | Target: ₹1,650\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\",\n                                                            children: \"Pending\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 731,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: \"Valid till: 30 days\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 734,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"INFY\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"GTT Sell: ₹1,420 | Stop Loss: ₹1,380\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 740,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                            children: \"Active\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 745,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 mt-1\",\n                                                            children: \"Valid till: 15 days\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 748,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 744,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 723,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                    lineNumber: 718,\n                    columnNumber: 11\n                }, undefined);\n            case \"current-holdings\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: [\n                                    \"\\uD83D\\uDCBC Current Holdings - \",\n                                    strategy?.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 761,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"WIPRO\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Qty: 50 | Avg Price: ₹420\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium text-green-600\",\n                                                            children: \"+₹1,250\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 772,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"+5.95%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 773,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 766,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"ICICIBANK\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Qty: 25 | Avg Price: ₹980\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 781,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium text-red-600\",\n                                                            children: \"-₹875\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 784,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"-3.57%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                            lineNumber: 785,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 783,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 764,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 760,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                    lineNumber: 759,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Select a tab\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                    lineNumber: 795,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Trading Strategies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 802,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"Manage and monitor your automated trading strategies\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 803,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                lineNumber: 801,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: strategies.map((strategy)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `cursor-pointer rounded-lg border-2 p-6 transition-all ${activeStrategy === strategy.id ? \"border-indigo-500 bg-indigo-50\" : \"border-gray-200 bg-white hover:border-gray-300\"}`,\n                            onClick: ()=>setActiveStrategy(strategy.id),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: strategy.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${strategy.status === \"Active\" ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"}`,\n                                            children: strategy.status\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 823,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 821,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-4\",\n                                    children: strategy.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 833,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: strategy.performance\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"Performance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 837,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 835,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: strategy.trades\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 840,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"Trades\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 841,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 839,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: strategy.winRate\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 844,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"Win Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                                    lineNumber: 845,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                            lineNumber: 843,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                    lineNumber: 834,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, strategy.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                            lineNumber: 812,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                    lineNumber: 810,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                lineNumber: 809,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8\",\n                        children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(tab.id),\n                                className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id ? \"border-indigo-500 text-indigo-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-2\",\n                                        children: tab.emoji\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                        lineNumber: 867,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    tab.name\n                                ]\n                            }, tab.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                                lineNumber: 858,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                        lineNumber: 856,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                    lineNumber: 855,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n                lineNumber: 854,\n                columnNumber: 7\n            }, undefined),\n            renderTabContent()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\strategies\\\\page.tsx\",\n        lineNumber: 800,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StrategiesPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/strategies/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ConditionalLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/ConditionalLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConditionalLayout: () => (/* binding */ ConditionalLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ ConditionalLayout auto */ \n\n\n\nconst ConditionalLayout = ({ children })=>{\n    const { isAuthenticated, isLoading, logout, user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Routes that should not show the main navigation\n    const NO_NAV_ROUTES = [\n        \"/\",\n        \"/auth/login\",\n        \"/auth/register\"\n    ];\n    const shouldShowNav = !NO_NAV_ROUTES.includes(pathname) && isAuthenticated;\n    // If loading, don't show any layout\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // Landing page and auth pages get minimal layout\n    if (NO_NAV_ROUTES.includes(pathname)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // Authenticated users get the full navigation layout\n    if (shouldShowNav) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: \"100vh\",\n                background: \"#f8fafc\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    style: {\n                        background: \"white\",\n                        padding: \"1rem\",\n                        borderBottom: \"1px solid #e5e7eb\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            maxWidth: \"1200px\",\n                            margin: \"0 auto\",\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"0.5rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"2rem\",\n                                            height: \"2rem\",\n                                            background: \"linear-gradient(135deg, #4F46E5, #3B82F6)\",\n                                            borderRadius: \"0.5rem\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: \"white\",\n                                                fontWeight: \"bold\"\n                                            },\n                                            children: \"₹\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        style: {\n                                            fontSize: \"1.5rem\",\n                                            fontWeight: \"bold\",\n                                            color: \"#4F46E5\"\n                                        },\n                                        children: \"Niveshtor\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"1rem\",\n                                    flexWrap: \"wrap\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/dashboard\",\n                                        style: {\n                                            color: \"#374151\",\n                                            textDecoration: \"none\",\n                                            padding: \"0.5rem 1rem\",\n                                            borderRadius: \"0.5rem\",\n                                            transition: \"background-color 0.2s\"\n                                        },\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/strategies\",\n                                        style: {\n                                            color: \"#374151\",\n                                            textDecoration: \"none\",\n                                            padding: \"0.5rem 1rem\",\n                                            borderRadius: \"0.5rem\",\n                                            transition: \"background-color 0.2s\"\n                                        },\n                                        children: \"Strategies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/universal/capital-management\",\n                                        style: {\n                                            color: \"#374151\",\n                                            textDecoration: \"none\",\n                                            padding: \"0.5rem 1rem\",\n                                            borderRadius: \"0.5rem\",\n                                            transition: \"background-color 0.2s\"\n                                        },\n                                        children: \"Universal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/backtesting\",\n                                        style: {\n                                            color: \"#374151\",\n                                            textDecoration: \"none\",\n                                            padding: \"0.5rem 1rem\",\n                                            borderRadius: \"0.5rem\",\n                                            transition: \"background-color 0.2s\"\n                                        },\n                                        children: \"Backtesting\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/broker-connect\",\n                                        style: {\n                                            color: \"#374151\",\n                                            textDecoration: \"none\",\n                                            padding: \"0.5rem 1rem\",\n                                            borderRadius: \"0.5rem\",\n                                            transition: \"background-color 0.2s\"\n                                        },\n                                        children: \"Broker Connect\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/trade-log\",\n                                        style: {\n                                            color: \"#374151\",\n                                            textDecoration: \"none\",\n                                            padding: \"0.5rem 1rem\",\n                                            borderRadius: \"0.5rem\",\n                                            transition: \"background-color 0.2s\"\n                                        },\n                                        children: \"Trade Log\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/settings\",\n                                        style: {\n                                            color: \"#374151\",\n                                            textDecoration: \"none\",\n                                            padding: \"0.5rem 1rem\",\n                                            borderRadius: \"0.5rem\",\n                                            transition: \"background-color 0.2s\"\n                                        },\n                                        children: \"Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            borderLeft: \"1px solid #e5e7eb\",\n                                            paddingLeft: \"1rem\",\n                                            marginLeft: \"0.5rem\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"0.5rem\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: \"#6B7280\",\n                                                    fontSize: \"0.875rem\"\n                                                },\n                                                children: user?.name || user?.email || \"User\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: logout,\n                                                style: {\n                                                    color: \"#EF4444\",\n                                                    background: \"none\",\n                                                    border: \"none\",\n                                                    cursor: \"pointer\",\n                                                    padding: \"0.5rem 1rem\",\n                                                    borderRadius: \"0.5rem\",\n                                                    fontSize: \"0.875rem\",\n                                                    fontWeight: \"500\",\n                                                    transition: \"background-color 0.2s\"\n                                                },\n                                                onMouseOver: (e)=>e.currentTarget.style.backgroundColor = \"#FEF2F2\",\n                                                onMouseOut: (e)=>e.currentTarget.style.backgroundColor = \"transparent\",\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    style: {\n                        padding: \"2rem\"\n                    },\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\ConditionalLayout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Default layout for other cases\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ConditionalLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/RouteGuard.tsx":
/*!***************************************!*\
  !*** ./src/components/RouteGuard.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RouteGuard: () => (/* binding */ RouteGuard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ RouteGuard auto */ \n\n\n\n// Routes that don't require authentication\nconst PUBLIC_ROUTES = [\n    \"/\",\n    \"/auth/login\",\n    \"/auth/register\"\n];\n// Routes that require authentication\nconst PROTECTED_ROUTES = [\n    \"/dashboard\",\n    \"/strategies\",\n    \"/universal\",\n    \"/capital-management\",\n    \"/backtesting\",\n    \"/broker-connect\",\n    \"/trade-log\",\n    \"/settings\"\n];\nconst RouteGuard = ({ children })=>{\n    const { isAuthenticated, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Don't redirect while authentication is still loading\n        if (isLoading) return;\n        // Check if current route is public\n        const isPublicRoute = PUBLIC_ROUTES.includes(pathname) || pathname.startsWith(\"/auth/\");\n        // Check if current route is protected\n        const isProtectedRoute = PROTECTED_ROUTES.some((route)=>pathname.startsWith(route));\n        console.log(\"RouteGuard:\", {\n            pathname,\n            isAuthenticated,\n            isPublicRoute,\n            isProtectedRoute,\n            isLoading\n        });\n        // If user is not authenticated and trying to access protected route\n        if (!isAuthenticated && isProtectedRoute) {\n            console.log(\"Redirecting unauthenticated user to landing page\");\n            router.push(\"/\");\n            return;\n        }\n        // If user is authenticated and on landing page or auth pages, redirect to dashboard\n        if (isAuthenticated && (pathname === \"/\" || pathname.startsWith(\"/auth/\"))) {\n            console.log(\"Redirecting authenticated user to dashboard\");\n            router.push(\"/dashboard\");\n            return;\n        }\n    }, [\n        isAuthenticated,\n        isLoading,\n        pathname,\n        router\n    ]);\n    // Show loading spinner while authentication is being checked\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\RouteGuard.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\RouteGuard.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\RouteGuard.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\RouteGuard.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, undefined);\n    }\n    // For protected routes, show loading if not authenticated (while redirect is happening)\n    const isProtectedRoute = PROTECTED_ROUTES.some((route)=>pathname.startsWith(route));\n    if (!isAuthenticated && isProtectedRoute) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-indigo-600 text-6xl mb-4\",\n                        children: \"\\uD83D\\uDD12\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\RouteGuard.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Authentication Required\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\RouteGuard.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"Redirecting to login...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\RouteGuard.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\RouteGuard.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\RouteGuard.tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\components\\\\RouteGuard.tsx\",\n            lineNumber: 85,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Sb3V0ZUd1YXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFeUM7QUFDZ0I7QUFDUDtBQU1sRCwyQ0FBMkM7QUFDM0MsTUFBTUssZ0JBQWdCO0lBQ3BCO0lBQ0E7SUFDQTtDQUNEO0FBRUQscUNBQXFDO0FBQ3JDLE1BQU1DLG1CQUFtQjtJQUN2QjtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0NBQ0Q7QUFFTSxNQUFNQyxhQUF3QyxDQUFDLEVBQUVDLFFBQVEsRUFBRTtJQUNoRSxNQUFNLEVBQUVDLGVBQWUsRUFBRUMsU0FBUyxFQUFFLEdBQUdOLDhEQUFPQTtJQUM5QyxNQUFNTyxTQUFTVCwwREFBU0E7SUFDeEIsTUFBTVUsV0FBV1QsNERBQVdBO0lBRTVCRixnREFBU0EsQ0FBQztRQUNSLHVEQUF1RDtRQUN2RCxJQUFJUyxXQUFXO1FBRWYsbUNBQW1DO1FBQ25DLE1BQU1HLGdCQUFnQlIsY0FBY1MsUUFBUSxDQUFDRixhQUFhQSxTQUFTRyxVQUFVLENBQUM7UUFFOUUsc0NBQXNDO1FBQ3RDLE1BQU1DLG1CQUFtQlYsaUJBQWlCVyxJQUFJLENBQUNDLENBQUFBLFFBQVNOLFNBQVNHLFVBQVUsQ0FBQ0c7UUFFNUVDLFFBQVFDLEdBQUcsQ0FBQyxlQUFlO1lBQ3pCUjtZQUNBSDtZQUNBSTtZQUNBRztZQUNBTjtRQUNGO1FBRUEsb0VBQW9FO1FBQ3BFLElBQUksQ0FBQ0QsbUJBQW1CTyxrQkFBa0I7WUFDeENHLFFBQVFDLEdBQUcsQ0FBQztZQUNaVCxPQUFPVSxJQUFJLENBQUM7WUFDWjtRQUNGO1FBRUEsb0ZBQW9GO1FBQ3BGLElBQUlaLG1CQUFvQkcsQ0FBQUEsYUFBYSxPQUFPQSxTQUFTRyxVQUFVLENBQUMsU0FBUSxHQUFJO1lBQzFFSSxRQUFRQyxHQUFHLENBQUM7WUFDWlQsT0FBT1UsSUFBSSxDQUFDO1lBQ1o7UUFDRjtJQUVGLEdBQUc7UUFBQ1o7UUFBaUJDO1FBQVdFO1FBQVVEO0tBQU87SUFFakQsNkRBQTZEO0lBQzdELElBQUlELFdBQVc7UUFDYixxQkFDRSw4REFBQ1k7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDQzt3QkFBRUQsV0FBVTtrQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSXJDO0lBRUEsd0ZBQXdGO0lBQ3hGLE1BQU1QLG1CQUFtQlYsaUJBQWlCVyxJQUFJLENBQUNDLENBQUFBLFFBQVNOLFNBQVNHLFVBQVUsQ0FBQ0c7SUFDNUUsSUFBSSxDQUFDVCxtQkFBbUJPLGtCQUFrQjtRQUN4QyxxQkFDRSw4REFBQ007WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FBZ0M7Ozs7OztrQ0FDL0MsOERBQUNFO3dCQUFHRixXQUFVO2tDQUF3Qzs7Ozs7O2tDQUN0RCw4REFBQ0M7d0JBQUVELFdBQVU7a0NBQXFCOzs7Ozs7a0NBQ2xDLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJdkI7SUFFQSxxQkFBTztrQkFBR2Y7O0FBQ1osRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL25pdmVzaHRvci1mcm9udGVuZC8uL3NyYy9jb21wb25lbnRzL1JvdXRlR3VhcmQudHN4PzFiMTciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyLCB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnLi4vY29udGV4dHMvQXV0aENvbnRleHQnO1xuXG5pbnRlcmZhY2UgUm91dGVHdWFyZFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuLy8gUm91dGVzIHRoYXQgZG9uJ3QgcmVxdWlyZSBhdXRoZW50aWNhdGlvblxuY29uc3QgUFVCTElDX1JPVVRFUyA9IFtcbiAgJy8nLFxuICAnL2F1dGgvbG9naW4nLFxuICAnL2F1dGgvcmVnaXN0ZXInXG5dO1xuXG4vLyBSb3V0ZXMgdGhhdCByZXF1aXJlIGF1dGhlbnRpY2F0aW9uXG5jb25zdCBQUk9URUNURURfUk9VVEVTID0gW1xuICAnL2Rhc2hib2FyZCcsXG4gICcvc3RyYXRlZ2llcycsXG4gICcvdW5pdmVyc2FsJyxcbiAgJy9jYXBpdGFsLW1hbmFnZW1lbnQnLFxuICAnL2JhY2t0ZXN0aW5nJyxcbiAgJy9icm9rZXItY29ubmVjdCcsXG4gICcvdHJhZGUtbG9nJyxcbiAgJy9zZXR0aW5ncydcbl07XG5cbmV4cG9ydCBjb25zdCBSb3V0ZUd1YXJkOiBSZWFjdC5GQzxSb3V0ZUd1YXJkUHJvcHM+ID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xuICBjb25zdCB7IGlzQXV0aGVudGljYXRlZCwgaXNMb2FkaW5nIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCBwYXRobmFtZSA9IHVzZVBhdGhuYW1lKCk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBEb24ndCByZWRpcmVjdCB3aGlsZSBhdXRoZW50aWNhdGlvbiBpcyBzdGlsbCBsb2FkaW5nXG4gICAgaWYgKGlzTG9hZGluZykgcmV0dXJuO1xuXG4gICAgLy8gQ2hlY2sgaWYgY3VycmVudCByb3V0ZSBpcyBwdWJsaWNcbiAgICBjb25zdCBpc1B1YmxpY1JvdXRlID0gUFVCTElDX1JPVVRFUy5pbmNsdWRlcyhwYXRobmFtZSkgfHwgcGF0aG5hbWUuc3RhcnRzV2l0aCgnL2F1dGgvJyk7XG4gICAgXG4gICAgLy8gQ2hlY2sgaWYgY3VycmVudCByb3V0ZSBpcyBwcm90ZWN0ZWRcbiAgICBjb25zdCBpc1Byb3RlY3RlZFJvdXRlID0gUFJPVEVDVEVEX1JPVVRFUy5zb21lKHJvdXRlID0+IHBhdGhuYW1lLnN0YXJ0c1dpdGgocm91dGUpKTtcblxuICAgIGNvbnNvbGUubG9nKCdSb3V0ZUd1YXJkOicsIHsgXG4gICAgICBwYXRobmFtZSwgXG4gICAgICBpc0F1dGhlbnRpY2F0ZWQsIFxuICAgICAgaXNQdWJsaWNSb3V0ZSwgXG4gICAgICBpc1Byb3RlY3RlZFJvdXRlLFxuICAgICAgaXNMb2FkaW5nIFxuICAgIH0pO1xuXG4gICAgLy8gSWYgdXNlciBpcyBub3QgYXV0aGVudGljYXRlZCBhbmQgdHJ5aW5nIHRvIGFjY2VzcyBwcm90ZWN0ZWQgcm91dGVcbiAgICBpZiAoIWlzQXV0aGVudGljYXRlZCAmJiBpc1Byb3RlY3RlZFJvdXRlKSB7XG4gICAgICBjb25zb2xlLmxvZygnUmVkaXJlY3RpbmcgdW5hdXRoZW50aWNhdGVkIHVzZXIgdG8gbGFuZGluZyBwYWdlJyk7XG4gICAgICByb3V0ZXIucHVzaCgnLycpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIElmIHVzZXIgaXMgYXV0aGVudGljYXRlZCBhbmQgb24gbGFuZGluZyBwYWdlIG9yIGF1dGggcGFnZXMsIHJlZGlyZWN0IHRvIGRhc2hib2FyZFxuICAgIGlmIChpc0F1dGhlbnRpY2F0ZWQgJiYgKHBhdGhuYW1lID09PSAnLycgfHwgcGF0aG5hbWUuc3RhcnRzV2l0aCgnL2F1dGgvJykpKSB7XG4gICAgICBjb25zb2xlLmxvZygnUmVkaXJlY3RpbmcgYXV0aGVudGljYXRlZCB1c2VyIHRvIGRhc2hib2FyZCcpO1xuICAgICAgcm91dGVyLnB1c2goJy9kYXNoYm9hcmQnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgfSwgW2lzQXV0aGVudGljYXRlZCwgaXNMb2FkaW5nLCBwYXRobmFtZSwgcm91dGVyXSk7XG5cbiAgLy8gU2hvdyBsb2FkaW5nIHNwaW5uZXIgd2hpbGUgYXV0aGVudGljYXRpb24gaXMgYmVpbmcgY2hlY2tlZFxuICBpZiAoaXNMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tc2xhdGUtNTAgdmlhLWJsdWUtNTAgdG8taW5kaWdvLTEwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzgwIGJhY2tkcm9wLWJsdXIteGwgcm91bmRlZC0zeGwgc2hhZG93LTJ4bCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHAtOCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItaW5kaWdvLTYwMCBteC1hdXRvIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+TG9hZGluZy4uLjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgLy8gRm9yIHByb3RlY3RlZCByb3V0ZXMsIHNob3cgbG9hZGluZyBpZiBub3QgYXV0aGVudGljYXRlZCAod2hpbGUgcmVkaXJlY3QgaXMgaGFwcGVuaW5nKVxuICBjb25zdCBpc1Byb3RlY3RlZFJvdXRlID0gUFJPVEVDVEVEX1JPVVRFUy5zb21lKHJvdXRlID0+IHBhdGhuYW1lLnN0YXJ0c1dpdGgocm91dGUpKTtcbiAgaWYgKCFpc0F1dGhlbnRpY2F0ZWQgJiYgaXNQcm90ZWN0ZWRSb3V0ZSkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLXNsYXRlLTUwIHZpYS1ibHVlLTUwIHRvLWluZGlnby0xMDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS84MCBiYWNrZHJvcC1ibHVyLXhsIHJvdW5kZWQtM3hsIHNoYWRvdy0yeGwgYm9yZGVyIGJvcmRlci13aGl0ZS8yMCBwLTggdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtaW5kaWdvLTYwMCB0ZXh0LTZ4bCBtYi00XCI+8J+UkjwvZGl2PlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+QXV0aGVudGljYXRpb24gUmVxdWlyZWQ8L2gyPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNlwiPlJlZGlyZWN0aW5nIHRvIGxvZ2luLi4uPC9wPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTggdy04IGJvcmRlci1iLTIgYm9yZGVyLWluZGlnby02MDAgbXgtYXV0b1wiPjwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gPD57Y2hpbGRyZW59PC8+O1xufTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUVmZmVjdCIsInVzZVJvdXRlciIsInVzZVBhdGhuYW1lIiwidXNlQXV0aCIsIlBVQkxJQ19ST1VURVMiLCJQUk9URUNURURfUk9VVEVTIiwiUm91dGVHdWFyZCIsImNoaWxkcmVuIiwiaXNBdXRoZW50aWNhdGVkIiwiaXNMb2FkaW5nIiwicm91dGVyIiwicGF0aG5hbWUiLCJpc1B1YmxpY1JvdXRlIiwiaW5jbHVkZXMiLCJzdGFydHNXaXRoIiwiaXNQcm90ZWN0ZWRSb3V0ZSIsInNvbWUiLCJyb3V0ZSIsImNvbnNvbGUiLCJsb2ciLCJwdXNoIiwiZGl2IiwiY2xhc3NOYW1lIiwicCIsImgyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/RouteGuard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/api */ \"(ssr)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider,withAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simplified auth check - just check localStorage without API calls\n        const checkAuth = ()=>{\n            try {\n                const token = localStorage.getItem(\"auth_token\");\n                const userInfo = localStorage.getItem(\"user_info\");\n                console.log(\"Checking authentication:\", {\n                    hasToken: !!token,\n                    hasUserInfo: !!userInfo\n                });\n                if (token && userInfo) {\n                    try {\n                        const userData = JSON.parse(userInfo);\n                        console.log(\"User data found in localStorage:\", userData);\n                        setUser(userData);\n                    } catch (error) {\n                        console.error(\"Error parsing user info:\", error);\n                        localStorage.removeItem(\"auth_token\");\n                        localStorage.removeItem(\"user_info\");\n                    }\n                } else {\n                    console.log(\"No authentication data found\");\n                }\n            } catch (error) {\n                console.error(\"Error checking authentication:\", error);\n                localStorage.removeItem(\"auth_token\");\n                localStorage.removeItem(\"user_info\");\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        // Use setTimeout to avoid blocking the initial render\n        setTimeout(checkAuth, 100);\n    }, []);\n    const login = async (email, password)=>{\n        try {\n            setIsLoading(true);\n            console.log(\"Attempting login for:\", email);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.login(email, password);\n            console.log(\"Login response:\", response);\n            if (response.success && response.data) {\n                // Check if this is demo mode (token starts with 'demo_token_')\n                const token = response.data.access_token;\n                if (token.startsWith(\"demo_token_\")) {\n                    // Demo mode - create mock user data\n                    const mockUser = {\n                        id: 1,\n                        name: \"Demo User\",\n                        email: email,\n                        phone: \"+91-9876543210\",\n                        created_at: new Date().toISOString()\n                    };\n                    setUser(mockUser);\n                    localStorage.setItem(\"user_info\", JSON.stringify(mockUser));\n                    return {\n                        success: true,\n                        message: response.message || \"Login successful (Demo Mode)\"\n                    };\n                } else {\n                    // Real backend mode - get user info from server\n                    try {\n                        const userResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\"}/api/auth/me`, {\n                            headers: {\n                                \"Authorization\": `Bearer ${token}`,\n                                \"Content-Type\": \"application/json\"\n                            }\n                        });\n                        if (userResponse.ok) {\n                            const userData = await userResponse.json();\n                            console.log(\"User data retrieved:\", userData);\n                            setUser(userData);\n                            localStorage.setItem(\"user_info\", JSON.stringify(userData));\n                            return {\n                                success: true,\n                                message: \"Login successful\"\n                            };\n                        } else {\n                            console.error(\"Failed to get user info after login\");\n                            return {\n                                success: false,\n                                message: \"Failed to get user information\"\n                            };\n                        }\n                    } catch (error) {\n                        console.error(\"Error getting user info:\", error);\n                        return {\n                            success: false,\n                            message: \"Failed to get user information\"\n                        };\n                    }\n                }\n            } else {\n                console.log(\"Login failed:\", response.message);\n                return {\n                    success: false,\n                    message: response.message || \"Login failed\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                message: \"Network error during login\"\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        try {\n            setIsLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.register(userData);\n            if (response.success) {\n                return {\n                    success: true,\n                    message: \"Registration successful. Please login.\"\n                };\n            } else {\n                return {\n                    success: false,\n                    message: response.message || \"Registration failed\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                message: \"Network error during registration\"\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = ()=>{\n        setUser(null);\n        _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.logout();\n        localStorage.removeItem(\"user_info\");\n    };\n    const value = {\n        user,\n        isAuthenticated: !!user,\n        isLoading,\n        login,\n        register,\n        logout\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, undefined);\n};\n// Higher-order component for protecting routes\nconst withAuth = (Component)=>{\n    return function AuthenticatedComponent(props) {\n        const { isAuthenticated, isLoading } = useAuth();\n        if (isLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this);\n        }\n        if (!isAuthenticated) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full space-y-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"mt-6 text-3xl font-extrabold text-gray-900\",\n                                children: \"Authentication Required\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm text-gray-600\",\n                                children: \"Please login to access this page\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/auth/login\",\n                                    className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\",\n                                    children: \"Go to Login\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 195,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 218,\n            columnNumber: 12\n        }, this);\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiService: () => (/* binding */ apiService)\n/* harmony export */ });\n/**\n * API Service for Niveshtor Trading Application\n * Handles all API calls with proper authentication and error handling\n */ const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\";\nclass ApiService {\n    getAuthHeaders() {\n        const token = localStorage.getItem(\"auth_token\");\n        return {\n            \"Content-Type\": \"application/json\",\n            ...token && {\n                \"Authorization\": `Bearer ${token}`\n            }\n        };\n    }\n    async handleResponse(response) {\n        try {\n            const data = await response.json();\n            if (response.ok) {\n                return {\n                    success: true,\n                    data: data,\n                    message: data.message\n                };\n            } else {\n                // Handle different error formats\n                const errorMessage = data.detail || data.message || `HTTP ${response.status}: ${response.statusText}`;\n                return {\n                    success: false,\n                    error: typeof errorMessage === \"string\" ? errorMessage : JSON.stringify(errorMessage),\n                    message: typeof errorMessage === \"string\" ? errorMessage : \"An error occurred\"\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                error: \"Failed to parse response\",\n                message: \"Network or parsing error occurred\"\n            };\n        }\n    }\n    // Authentication APIs\n    async login(email, password) {\n        try {\n            const response = await fetch(`${API_BASE_URL}/api/auth/login`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const result = await this.handleResponse(response);\n            if (result.success && result.data) {\n                localStorage.setItem(\"auth_token\", result.data.access_token);\n            }\n            return result;\n        } catch (error) {\n            console.log(\"Backend server not available, using demo mode\");\n            // Demo mode: Check for demo credentials\n            if (email === \"<EMAIL>\" && password === \"demo123\") {\n                return new Promise((resolve)=>{\n                    setTimeout(()=>{\n                        const mockToken = \"demo_token_\" + Date.now();\n                        localStorage.setItem(\"auth_token\", mockToken);\n                        resolve({\n                            success: true,\n                            message: \"Login successful! (Demo Mode)\",\n                            data: {\n                                access_token: mockToken,\n                                token_type: \"Bearer\"\n                            }\n                        });\n                    }, 800); // Simulate network delay\n                });\n            } else {\n                return {\n                    success: false,\n                    error: \"Invalid credentials\",\n                    message: \"Invalid email or password. Try <EMAIL> / demo123 (Demo Mode - Backend server not running)\"\n                };\n            }\n        }\n    }\n    async register(userData) {\n        try {\n            const response = await fetch(`${API_BASE_URL}/api/auth/register`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(userData)\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            console.log(\"Backend server not available, using demo mode\");\n            // Demo mode: Simulate successful registration\n            return new Promise((resolve)=>{\n                setTimeout(()=>{\n                    resolve({\n                        success: true,\n                        message: \"Account created successfully! (Demo Mode - Backend server not running)\",\n                        data: {\n                            user: {\n                                id: Date.now(),\n                                name: userData.name,\n                                email: userData.email,\n                                phone: userData.phone\n                            }\n                        }\n                    });\n                }, 1000); // Simulate network delay\n            });\n        }\n    }\n    logout() {\n        localStorage.removeItem(\"auth_token\");\n    }\n    // Broker Connection APIs\n    async createBrokerConnection(connectionData) {\n        try {\n            const response = await fetch(`${API_BASE_URL}/api/broker/connections`, {\n                method: \"POST\",\n                headers: this.getAuthHeaders(),\n                body: JSON.stringify(connectionData)\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            return {\n                success: false,\n                error: \"Network error\",\n                message: \"Failed to connect to server\"\n            };\n        }\n    }\n    async getBrokerConnections() {\n        try {\n            const response = await fetch(`${API_BASE_URL}/api/broker/connections`, {\n                method: \"GET\",\n                headers: this.getAuthHeaders()\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            return {\n                success: false,\n                error: \"Network error\",\n                message: \"Failed to connect to server\"\n            };\n        }\n    }\n    async manageBrokerConnection(connectionId, action) {\n        try {\n            const response = await fetch(`${API_BASE_URL}/api/broker/connections/${connectionId}/action`, {\n                method: \"POST\",\n                headers: this.getAuthHeaders(),\n                body: JSON.stringify({\n                    connection_id: connectionId,\n                    action\n                })\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            return {\n                success: false,\n                error: \"Network error\",\n                message: \"Failed to connect to server\"\n            };\n        }\n    }\n    async deleteBrokerConnection(connectionId) {\n        try {\n            const response = await fetch(`${API_BASE_URL}/api/broker/connections/${connectionId}`, {\n                method: \"DELETE\",\n                headers: this.getAuthHeaders()\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            return {\n                success: false,\n                error: \"Network error\",\n                message: \"Failed to connect to server\"\n            };\n        }\n    }\n    async getConnectionStats() {\n        try {\n            const response = await fetch(`${API_BASE_URL}/api/broker/connection-stats`, {\n                method: \"GET\",\n                headers: this.getAuthHeaders()\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            return {\n                success: false,\n                error: \"Network error\",\n                message: \"Failed to connect to server\"\n            };\n        }\n    }\n    async getMarketData(symbols = \"RELIANCE,TCS,HDFC,INFY\") {\n        try {\n            const response = await fetch(`${API_BASE_URL}/api/broker/market-data?symbols=${symbols}`, {\n                method: \"GET\",\n                headers: this.getAuthHeaders()\n            });\n            return await this.handleResponse(response);\n        } catch (error) {\n            return {\n                success: false,\n                error: \"Network error\",\n                message: \"Failed to connect to server\"\n            };\n        }\n    }\n    // Utility method to check if user is authenticated\n    isAuthenticated() {\n        return !!localStorage.getItem(\"auth_token\");\n    }\n    // Utility method to get current user info (if stored)\n    getCurrentUser() {\n        const userInfo = localStorage.getItem(\"user_info\");\n        return userInfo ? JSON.parse(userInfo) : null;\n    }\n}\n// Export singleton instance\nconst apiService = new ApiService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/api.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7e5dfbc38e81\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbml2ZXNodG9yLWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9hMjM5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiN2U1ZGZiYzM4ZTgxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_RouteGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/RouteGuard */ \"(rsc)/./src/components/RouteGuard.tsx\");\n/* harmony import */ var _components_ConditionalLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ConditionalLayout */ \"(rsc)/./src/components/ConditionalLayout.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Niveshtor - Trading Platform\",\n    description: \"Advanced trading platform for Indian stock markets\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RouteGuard__WEBPACK_IMPORTED_MODULE_3__.RouteGuard, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConditionalLayout__WEBPACK_IMPORTED_MODULE_4__.ConditionalLayout, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Niveshtor\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/strategies/page.tsx":
/*!*************************************!*\
  !*** ./src/app/strategies/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Niveshtor\frontend\src\app\strategies\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/ConditionalLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/ConditionalLayout.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConditionalLayout: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Niveshtor\frontend\src\components\ConditionalLayout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Niveshtor\frontend\src\components\ConditionalLayout.tsx#ConditionalLayout`);


/***/ }),

/***/ "(rsc)/./src/components/RouteGuard.tsx":
/*!***************************************!*\
  !*** ./src/components/RouteGuard.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RouteGuard: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Niveshtor\frontend\src\components\RouteGuard.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Niveshtor\frontend\src\components\RouteGuard.tsx#RouteGuard`);


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e1),
/* harmony export */   useAuth: () => (/* binding */ e0),
/* harmony export */   withAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Niveshtor\frontend\src\contexts\AuthContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Niveshtor\frontend\src\contexts\AuthContext.tsx#useAuth`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Niveshtor\frontend\src\contexts\AuthContext.tsx#AuthProvider`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Niveshtor\frontend\src\contexts\AuthContext.tsx#withAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fstrategies%2Fpage&page=%2Fstrategies%2Fpage&appPaths=%2Fstrategies%2Fpage&pagePath=private-next-app-dir%2Fstrategies%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahesh%5CDocuments%5CNiveshtor%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();