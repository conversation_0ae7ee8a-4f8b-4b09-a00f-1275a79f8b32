#!/usr/bin/env python3
"""
Bulletproof FastAPI server - Absolutely minimal and stable
"""

from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Create FastAPI app
app = FastAPI(title="Bulletproof API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def read_root():
    return {"message": "Bulletproof server is running", "status": "healthy"}

@app.post("/api/auth/login")
def login(request: dict):
    """Super simple login - always succeeds"""
    return {
        "access_token": "demo-token-12345", 
        "token_type": "bearer"
    }

@app.get("/api/boh/eligible")
def get_boh_stocks():
    """Super simple BOH endpoint - always returns same data"""
    return {
        "stocks": [
            {
                "stock_name": "RELIANCE",
                "cmp": 2450.50,
                "week_52_low": 2100.00,
                "week_52_low_date": "2024-03-15",
                "week_52_high": 2800.00,
                "week_52_high_date": "2024-01-10",
                "boh_eligible": "Yes",
                "is_eligible": True
            }
        ],
        "total_count": 1,
        "eligible_count": 1,
        "connection_status": {
            "connected": True,
            "status": "Bulletproof mode - always works"
        }
    }

if __name__ == "__main__":
    print("🚀 Bulletproof Server Starting on port 8001...")
    uvicorn.run(app, host="127.0.0.1", port=8001, log_level="info")
