#!/usr/bin/env python3
"""
Simple HTTP server without FastAPI - Just to get things working
"""

from http.server import <PERSON><PERSON><PERSON>erver, BaseHTTPRequestHandler
import json
import urllib.parse

class SimpleHandler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        if self.path == '/':
            response = {"message": "Simple server is working", "status": "healthy"}
        elif self.path == '/api/boh/eligible':
            response = {
                "stocks": [
                    {
                        "stock_name": "RELIANCE",
                        "cmp": 2450.50,
                        "week_52_low": 2100.00,
                        "week_52_low_date": "2024-03-15",
                        "week_52_high": 2800.00,
                        "week_52_high_date": "2024-01-10",
                        "boh_eligible": "Yes",
                        "is_eligible": True
                    },
                    {
                        "stock_name": "TCS",
                        "cmp": 3650.75,
                        "week_52_low": 3200.00,
                        "week_52_low_date": "2024-02-20",
                        "week_52_high": 4100.00,
                        "week_52_high_date": "2024-01-05",
                        "boh_eligible": "Yes",
                        "is_eligible": True
                    }
                ],
                "total_count": 2,
                "eligible_count": 2,
                "connection_status": {
                    "connected": True,
                    "status": "Simple HTTP server - working!"
                }
            }
        else:
            response = {"error": "Not found"}
            
        self.wfile.write(json.dumps(response).encode())

    def do_POST(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        if self.path == '/api/auth/login':
            response = {
                "access_token": "simple-token-12345", 
                "token_type": "bearer"
            }
        else:
            response = {"error": "Not found"}
            
        self.wfile.write(json.dumps(response).encode())

if __name__ == "__main__":
    server = HTTPServer(('localhost', 8000), SimpleHandler)
    print("🚀 Simple HTTP Server starting on http://localhost:8000")
    print("✅ This should work without any issues!")
    server.serve_forever()
