'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useSearchParams } from 'next/navigation';

// Interface for BOH eligible stocks
interface BOHStock {
  stock_name: string;
  cmp: number;
  week_52_low: number;
  week_52_low_date: string;
  week_52_high: number;
  week_52_high_date: string;
  boh_eligible: string;
  is_eligible: boolean;
}

// Interface for Weekly High Signal data
interface WeeklyHighSignal {
  stock_name: string;
  cmp: number;
  already_purchased: boolean;
  last_week_highest_price: number;
  suggested_buy_price: number;
  percentage_difference: number;
  suggested_gtt_quantity: number;
  weekly_ohlc_data: WeeklyOHLCData[];
  data_source: 'yahoo_finance' | 'unavailable';
}

// Interface for OHLC data
interface WeeklyOHLCData {
  date: string;
  day: string;
  open: number;
  high: number;
  low: number;
  close: number;
}

// Interface for user holdings
interface UserHolding {
  symbol: string;
  quantity: number;
  avg_price: number;
}

const StrategiesPage = () => {
  const { isAuthenticated, user } = useAuth();
  const searchParams = useSearchParams();
  const [activeStrategy, setActiveStrategy] = useState('weekly-high');

  // Weekly High Strategy state
  const [weeklyHighSignals, setWeeklyHighSignals] = useState<WeeklyHighSignal[]>([]);
  const [bohStocks, setBohStocks] = useState<BOHStock[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [processingStatus, setProcessingStatus] = useState<string>('');

  const strategies = [
    {
      id: 'weekly-high',
      name: 'Weekly High',
      description: 'Breakout strategy based on weekly high levels',
      status: 'Active',
      performance: '+12.5%',
      trades: 45,
      winRate: '68%'
    },
    {
      id: 'rsi',
      name: 'RSI Mean Reversion',
      description: 'Mean reversion strategy using RSI indicator',
      status: 'Active',
      performance: '+8.3%',
      trades: 32,
      winRate: '72%'
    },
    {
      id: 'consolidated-stock',
      name: 'Consolidated Stock',
      description: 'Breakout from consolidation patterns',
      status: 'Paused',
      performance: '+5.1%',
      trades: 18,
      winRate: '61%'
    }
  ];

  const tabs = [
    { id: 'signal', name: 'Signal', emoji: '📊' },
    { id: 'gtt-order', name: 'GTT Order', emoji: '⚡' },
    { id: 'current-holdings', name: 'Current Holdings', emoji: '💼' }
  ];

  const [activeTab, setActiveTab] = useState('signal');

  // Handle URL parameters for strategy selection
  useEffect(() => {
    const strategy = searchParams.get('strategy');
    if (strategy && ['weekly-high', 'rsi', 'consolidated-stock'].includes(strategy)) {
      setActiveStrategy(strategy);
    }
  }, [searchParams]);

  // Weekly High Strategy Functions
  const fetchWeeklyHighSignals = async () => {
    try {
      setLoading(true);
      setError(null);

      // Always try to get a fresh token to avoid 401 errors
      console.log('🔐 Getting fresh authentication token for Weekly High Strategy...');
      console.log('🌐 Backend server URL: http://localhost:8000');
      setProcessingStatus('Authenticating with demo credentials...');

      const loginResponse = await fetch('http://localhost:8000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'demo123'
        })
      });

      console.log('📡 Login response status:', loginResponse.status, loginResponse.statusText);

      if (!loginResponse.ok) {
        const errorText = await loginResponse.text();
        console.error('❌ Weekly High Strategy login failed:', loginResponse.status, errorText);
        throw new Error(`Authentication failed: ${loginResponse.status} ${loginResponse.statusText}. Error: ${errorText}`);
      }

      const loginData = await loginResponse.json();
      const token = loginData.access_token;
      localStorage.setItem('auth_token', token);
      console.log('✅ Fresh token obtained for Weekly High Strategy');

      // Step 1: Fetch BOH eligible stocks from backend API with extended timeout
      console.log('🔍 Fetching BOH eligible stocks from backend...');
      setProcessingStatus('Fetching BOH eligible stocks from backend...');

      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.log('⏰ BOH API request timed out after 60 seconds');
        controller.abort();
      }, 60000); // Extended to 60 second timeout for BOH API

      let eligibleStocks: BOHStock[] = [];

      try {
        console.log('🌐 Making BOH API request with token:', token ? token.substring(0, 20) + '...' : 'No token');
        const bohResponse = await fetch('http://localhost:8000/api/boh/eligible', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId);
        console.log('📡 BOH API response status:', bohResponse.status, bohResponse.statusText);

        if (!bohResponse.ok) {
          const errorText = await bohResponse.text();
          console.error('❌ BOH API error response:', errorText);
          throw new Error(`BOH API returned ${bohResponse.status}: ${bohResponse.statusText}`);
        }

        const bohData = await bohResponse.json();
        console.log('📊 BOH API data received:', {
          totalStocks: bohData.stocks?.length || 0,
          eligibleCount: bohData.eligible_count || 0
        });

        // Filter only BOH eligible stocks (boh_eligible = "Yes")
        eligibleStocks = bohData.stocks.filter((stock: BOHStock) =>
          stock.is_eligible && stock.boh_eligible === 'Yes'
        );

        setBohStocks(eligibleStocks);
        console.log(`✅ Found ${eligibleStocks.length} BOH eligible stocks from backend`);

        if (eligibleStocks.length === 0) {
          setWeeklyHighSignals([]);
          setLastUpdated(new Date());
          setProcessingStatus('');
          console.log('⚠️ No BOH eligible stocks found');
          return;
        }
      } catch (error: any) {
        clearTimeout(timeoutId);
        console.error('❌ Failed to fetch BOH eligible stocks:', error);

        // Provide more specific error messages
        if (error.name === 'AbortError') {
          throw new Error('BOH API request timed out. The backend may be processing a large number of stocks. Please try again.');
        } else if (error.message?.includes('Failed to fetch')) {
          throw new Error('Unable to connect to the backend server. Please check if the server is running.');
        } else {
          throw new Error(`Failed to fetch BOH eligible stocks: ${error.message}`);
        }
      }

      // Step 2: Calculate Weekly High signals with real Yahoo Finance data
      console.log('📈 Calculating Weekly High signals with Yahoo Finance data...');
      setProcessingStatus(`Processing ${eligibleStocks.length} BOH eligible stocks for Weekly High signals...`);

      const signals = await calculateWeeklyHighSignals(eligibleStocks, []);
      setWeeklyHighSignals(signals);
      setLastUpdated(new Date());
      setProcessingStatus('');

      console.log(`🎯 Generated ${signals.length} Weekly High signals from ${eligibleStocks.length} BOH eligible stocks`);
    } catch (error) {
      console.error('Error in fetchWeeklyHighSignals:', error);
      setError(error instanceof Error ? error.message : 'Unknown error occurred');
      setProcessingStatus('');
    } finally {
      setLoading(false);
    }
  };

  const calculateWeeklyHighSignals = async (eligibleStocks: BOHStock[], holdings: UserHolding[]): Promise<WeeklyHighSignal[]> => {
    const signals: WeeklyHighSignal[] = [];
    const holdingSymbols = new Set(holdings.map(h => h.symbol));
    let successCount = 0;
    let failureCount = 0;

    console.log(`📊 Processing ${eligibleStocks.length} BOH eligible stocks for Weekly High signals...`);

    for (let i = 0; i < eligibleStocks.length; i++) {
      const stock = eligibleStocks[i];
      try {
        console.log(`🔍 Processing ${i + 1}/${eligibleStocks.length}: ${stock.stock_name}...`);
        setProcessingStatus(`Processing ${i + 1}/${eligibleStocks.length}: ${stock.stock_name} (✅${successCount} ❌${failureCount})`);

        // Check if already purchased
        const alreadyPurchased = holdingSymbols.has(stock.stock_name);

        // Try multiple approaches to get OHLC data
        let weeklyOHLCData: WeeklyOHLCData[] = [];
        let lastWeekHighestPrice = 0;
        let dataSource: 'yahoo_finance' | 'unavailable' = 'yahoo_finance';

        try {
          // First attempt: Fetch real OHLC data from Yahoo Finance
          weeklyOHLCData = await fetchYahooFinanceOHLC(stock.stock_name);
          lastWeekHighestPrice = getLastWeekHighestPrice(weeklyOHLCData);
          dataSource = 'yahoo_finance';
        } catch (yahooError) {
          console.log(`⚠️ Yahoo Finance failed for ${stock.stock_name}, marking as data unavailable`);

          // No fallback calculation - mark as unavailable
          dataSource = 'unavailable';
          lastWeekHighestPrice = 0;
          weeklyOHLCData = [];
        }

        // Calculate suggested buy price and other metrics
        const suggestedBuyPrice = dataSource === 'yahoo_finance' ? lastWeekHighestPrice + 0.05 : 0;
        const percentageDifference = dataSource === 'yahoo_finance' ?
          ((stock.cmp - suggestedBuyPrice) / suggestedBuyPrice) * 100 : 0;
        const suggestedGTTQuantity = dataSource === 'yahoo_finance' ?
          Math.floor(2000 / suggestedBuyPrice) : 0;

        const signal: WeeklyHighSignal = {
          stock_name: stock.stock_name,
          cmp: stock.cmp,
          already_purchased: alreadyPurchased,
          last_week_highest_price: lastWeekHighestPrice,
          suggested_buy_price: suggestedBuyPrice,
          percentage_difference: percentageDifference,
          suggested_gtt_quantity: suggestedGTTQuantity,
          weekly_ohlc_data: weeklyOHLCData,
          data_source: dataSource
        };

        signals.push(signal);

        if (dataSource === 'yahoo_finance') {
          successCount++;
          console.log(`✅ ${stock.stock_name}: Weekly High = ₹${lastWeekHighestPrice.toFixed(2)}, Buy Price = ₹${suggestedBuyPrice.toFixed(2)}, Diff = ${percentageDifference.toFixed(2)}%`);
        } else {
          failureCount++;
          console.log(`❌ ${stock.stock_name}: Data unavailable`);
        }

        // Add delay to avoid overwhelming the API
        if (i < eligibleStocks.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }

      } catch (error) {
        failureCount++;
        console.error(`❌ Error processing ${stock.stock_name}:`, error);

        // Add signal with unavailable data
        const signal: WeeklyHighSignal = {
          stock_name: stock.stock_name,
          cmp: stock.cmp,
          already_purchased: holdingSymbols.has(stock.stock_name),
          last_week_highest_price: 0,
          suggested_buy_price: 0,
          percentage_difference: 0,
          suggested_gtt_quantity: 0,
          weekly_ohlc_data: [],
          data_source: 'unavailable'
        };
        signals.push(signal);
      }
    }

    console.log(`🎯 Successfully generated ${signals.length} Weekly High signals from ${eligibleStocks.length} BOH eligible stocks (✅${successCount} ❌${failureCount})`);
    setProcessingStatus('');
    return signals;
  };

  const fetchYahooFinanceOHLC = async (stockSymbol: string): Promise<WeeklyOHLCData[]> => {
    // Try multiple approaches to get data
    const attempts = [
      // Attempt 1: Current week data
      () => fetchYahooDataWithDates(stockSymbol, 0),
      // Attempt 2: Previous week data
      () => fetchYahooDataWithDates(stockSymbol, 1),
      // Attempt 3: Two weeks ago data
      () => fetchYahooDataWithDates(stockSymbol, 2)
    ];

    for (let i = 0; i < attempts.length; i++) {
      try {
        console.log(`📊 Attempt ${i + 1}/3: Fetching OHLC data for ${stockSymbol}...`);
        const data = await attempts[i]();
        if (data && data.length > 0) {
          console.log(`✅ Successfully fetched ${data.length} days of OHLC data for ${stockSymbol} (attempt ${i + 1})`);
          return data;
        }
      } catch (error) {
        console.log(`⚠️ Attempt ${i + 1} failed for ${stockSymbol}:`, error);
        if (i === attempts.length - 1) {
          throw error;
        }
      }
    }

    throw new Error(`Failed to fetch OHLC data for ${stockSymbol} after ${attempts.length} attempts`);
  };

  const fetchYahooDataWithDates = async (stockSymbol: string, weeksBack: number): Promise<WeeklyOHLCData[]> => {
    const now = new Date();
    const startDate = new Date(now);
    startDate.setDate(now.getDate() - (7 * (weeksBack + 1))); // Go back weeksBack+1 weeks
    const endDate = new Date(now);
    endDate.setDate(now.getDate() - (7 * weeksBack)); // End weeksBack weeks ago

    const formatDate = (date: Date) => {
      return date.toISOString().split('T')[0];
    };

    const startDateStr = formatDate(startDate);
    const endDateStr = formatDate(endDate);

    console.log(`📅 Fetching ${stockSymbol} data from ${startDateStr} to ${endDateStr} (${weeksBack} weeks back)`);

    const corsProxy = 'https://cors-anywhere.herokuapp.com/';
    const yahooUrl = `${corsProxy}https://query1.finance.yahoo.com/v7/finance/download/${stockSymbol}.NS?period1=${Math.floor(startDate.getTime() / 1000)}&period2=${Math.floor(endDate.getTime() / 1000)}&interval=1d&events=history`;

    const response = await fetch(yahooUrl, {
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    if (!response.ok) {
      throw new Error(`Yahoo Finance API error: ${response.status} ${response.statusText}`);
    }

    const csvText = await response.text();
    const lines = csvText.trim().split('\n');

    if (lines.length <= 1) {
      throw new Error('No data available from Yahoo Finance');
    }

    const ohlcData: WeeklyOHLCData[] = [];

    // Skip header row
    for (let i = 1; i < lines.length; i++) {
      const columns = lines[i].split(',');
      if (columns.length >= 6) {
        const date = columns[0];
        const open = parseFloat(columns[1]);
        const high = parseFloat(columns[2]);
        const low = parseFloat(columns[3]);
        const close = parseFloat(columns[4]);

        if (!isNaN(open) && !isNaN(high) && !isNaN(low) && !isNaN(close)) {
          const dayOfWeek = new Date(date).toLocaleDateString('en-US', { weekday: 'long' });

          ohlcData.push({
            date,
            day: dayOfWeek,
            open: Math.round(open * 100) / 100,
            high: Math.round(high * 100) / 100,
            low: Math.round(low * 100) / 100,
            close: Math.round(close * 100) / 100
          });
        }
      }
    }

    if (ohlcData.length === 0) {
      throw new Error('No valid OHLC data found');
    }

    return ohlcData;
  };

  const getLastWeekHighestPrice = (ohlcData: WeeklyOHLCData[]): number => {
    if (ohlcData.length === 0) return 0;

    // Find the maximum 'High' value from all days
    const weeklyHigh = Math.max(...ohlcData.map(day => day.high));
    return Math.round(weeklyHigh * 100) / 100;
  };

  const toggleRowExpansion = (stockName: string) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(stockName)) {
      newExpandedRows.delete(stockName);
    } else {
      newExpandedRows.add(stockName);
    }
    setExpandedRows(newExpandedRows);
  };

  // Load data when strategy changes
  useEffect(() => {
    if (activeStrategy === 'weekly-high') {
      // Always try to fetch signals, the function handles authentication internally
      fetchWeeklyHighSignals();
    }
  }, [activeStrategy]);

  const renderWeeklyHighSignalTab = () => {
    return (
      <div className="space-y-6">
        {/* Header with Refresh Button */}
        <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent">
                📈 Weekly High Strategy - Signal
              </h2>
              <p className="text-gray-600 mt-2">
                Breakout opportunities from BOH eligible stocks with strong weekly price structure
              </p>
            </div>
            <button
              onClick={fetchWeeklyHighSignals}
              disabled={loading}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing...
                </>
              ) : (
                <>
                  🔄 Refresh Signals
                </>
              )}
            </button>
          </div>

          {/* Processing Status */}
          {processingStatus && (
            <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
                <span className="text-blue-800 text-sm font-medium">{processingStatus}</span>
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-red-600 text-2xl mr-3">❌</span>
                  <div>
                    <h4 className="text-red-800 font-medium">Error</h4>
                    <p className="text-red-600 text-sm">{error}</p>
                  </div>
                </div>
                <button
                  onClick={() => {
                    setError(null);
                    fetchWeeklyHighSignals();
                  }}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Retry
                </button>
              </div>
            </div>
          )}

          {/* Last Updated */}
          {lastUpdated && (
            <div className="mb-4 text-sm text-gray-600">
              <span className="font-medium">Last Updated:</span> {lastUpdated.toLocaleString()}
            </div>
          )}

          {/* Signals Table */}
          {weeklyHighSignals.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CMP</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Weekly High</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data Source</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Buy Price</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">% Diff</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GTT Qty</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">OHLC Data</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {weeklyHighSignals.map((signal, index) => (
                    <React.Fragment key={signal.stock_name}>
                      <tr className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{signal.stock_name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">₹{signal.cmp.toFixed(2)}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {signal.data_source === 'unavailable' ? 'Data Not Available' : `₹${signal.last_week_highest_price.toFixed(2)}`}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            signal.data_source === 'yahoo_finance'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {signal.data_source === 'yahoo_finance' ? '📊 Real Data' : '❌ Unavailable'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-blue-600">
                            {signal.data_source === 'unavailable' ? 'N/A' : `₹${signal.suggested_buy_price.toFixed(2)}`}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`text-sm font-medium ${
                            signal.data_source === 'unavailable' ? 'text-gray-500' :
                            signal.percentage_difference >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {signal.data_source === 'unavailable' ? 'N/A' :
                             `${signal.percentage_difference >= 0 ? '+' : ''}${signal.percentage_difference.toFixed(2)}%`}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {signal.data_source === 'unavailable' ? 'N/A' : signal.suggested_gtt_quantity}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <button
                            onClick={() => toggleRowExpansion(signal.stock_name)}
                            className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs leading-4 font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                          >
                            {expandedRows.has(signal.stock_name) ? '📊 Hide OHLC' : '📊 Show OHLC'}
                          </button>
                        </td>
                      </tr>

                      {/* Collapsible OHLC Data Row */}
                      {expandedRows.has(signal.stock_name) && (
                        <tr>
                          <td colSpan={8} className="px-6 py-4 bg-gray-50">
                            <div className="space-y-3">
                              <h4 className="text-sm font-medium text-gray-900">
                                📈 Weekly OHLC Data for {signal.stock_name}
                              </h4>
                              {signal.data_source === 'unavailable' ? (
                                <div className="text-center py-8">
                                  <div className="text-red-500 text-lg mb-2">❌</div>
                                  <p className="text-sm text-gray-600">Yahoo Finance data could not be retrieved for this stock</p>
                                  <p className="text-xs text-gray-500 mt-1">No OHLC data available for signal calculation</p>
                                </div>
                              ) : (
                                <div className="overflow-x-auto">
                                  <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-100">
                                      <tr>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Day</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Open</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">High</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Low</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Close</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Weekly High</th>
                                      </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                      {signal.weekly_ohlc_data.map((ohlc, ohlcIndex) => (
                                        <tr key={ohlcIndex}>
                                          <td className="px-4 py-2 text-sm text-gray-900">{ohlc.day}</td>
                                          <td className="px-4 py-2 text-sm text-gray-900">{ohlc.date}</td>
                                          <td className="px-4 py-2 text-sm text-gray-900">₹{ohlc.open.toFixed(2)}</td>
                                          <td className="px-4 py-2 text-sm font-medium text-green-600">₹{ohlc.high.toFixed(2)}</td>
                                          <td className="px-4 py-2 text-sm text-gray-900">₹{ohlc.low.toFixed(2)}</td>
                                          <td className="px-4 py-2 text-sm text-gray-900">₹{ohlc.close.toFixed(2)}</td>
                                          <td className="px-4 py-2 text-sm font-bold text-blue-600">
                                            {ohlc.high === signal.last_week_highest_price ? `₹${signal.last_week_highest_price.toFixed(2)}` : ''}
                                          </td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              )}
                              {signal.data_source === 'yahoo_finance' && (
                                <div className="text-xs text-gray-600 mt-2">
                                  <p><strong>Calculation:</strong> Weekly High = Maximum of all 'High' values = ₹{signal.last_week_highest_price.toFixed(2)}</p>
                                  <p><strong>Suggested Buy Price:</strong> ₹{signal.last_week_highest_price.toFixed(2)} + ₹0.05 = ₹{signal.suggested_buy_price.toFixed(2)}</p>
                                </div>
                              )}
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">📊</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Weekly High signals available</h3>
              <p className="text-gray-600">
                {loading ? 'Loading signals...' : 'Ensure BOH eligible stocks are available and try refreshing.'}
              </p>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderTabContent = () => {
    const strategy = strategies.find(s => s.id === activeStrategy);

    switch (activeTab) {
      case 'signal':
        if (activeStrategy === 'weekly-high') {
          return renderWeeklyHighSignalTab();
        }
        return (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                📈 Current Signals - {strategy?.name}
              </h3>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-medium text-gray-900">RELIANCE</h4>
                      <p className="text-sm text-gray-600">Signal: BUY | Price: ₹2,450</p>
                    </div>
                    <div className="text-right">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Strong Buy
                      </span>
                      <p className="text-sm text-gray-600 mt-1">Confidence: 85%</p>
                    </div>
                  </div>
                </div>
                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-medium text-gray-900">TCS</h4>
                      <p className="text-sm text-gray-600">Signal: BUY | Price: ₹3,680</p>
                    </div>
                    <div className="text-right">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Buy
                      </span>
                      <p className="text-sm text-gray-600 mt-1">Confidence: 72%</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      
      case 'gtt-order':
        return (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                ⚡ GTT Orders - {strategy?.name}
              </h3>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-medium text-gray-900">HDFC</h4>
                      <p className="text-sm text-gray-600">GTT Buy: ₹1,580 | Target: ₹1,650</p>
                    </div>
                    <div className="text-right">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Pending
                      </span>
                      <p className="text-sm text-gray-600 mt-1">Valid till: 30 days</p>
                    </div>
                  </div>
                </div>
                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-medium text-gray-900">INFY</h4>
                      <p className="text-sm text-gray-600">GTT Sell: ₹1,420 | Stop Loss: ₹1,380</p>
                    </div>
                    <div className="text-right">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Active
                      </span>
                      <p className="text-sm text-gray-600 mt-1">Valid till: 15 days</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      
      case 'current-holdings':
        return (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                💼 Current Holdings - {strategy?.name}
              </h3>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-medium text-gray-900">WIPRO</h4>
                      <p className="text-sm text-gray-600">Qty: 50 | Avg Price: ₹420</p>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-medium text-green-600">+₹1,250</p>
                      <p className="text-sm text-gray-600">+5.95%</p>
                    </div>
                  </div>
                </div>
                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-medium text-gray-900">ICICIBANK</h4>
                      <p className="text-sm text-gray-600">Qty: 25 | Avg Price: ₹980</p>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-medium text-red-600">-₹875</p>
                      <p className="text-sm text-gray-600">-3.57%</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      
      default:
        return <div>Select a tab</div>;
    }
  };

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Trading Strategies</h1>
        <p className="mt-2 text-gray-600">
          Manage and monitor your automated trading strategies
        </p>
      </div>

      {/* Strategy Selection */}
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {strategies.map((strategy) => (
            <div
              key={strategy.id}
              className={`cursor-pointer rounded-lg border-2 p-6 transition-all ${
                activeStrategy === strategy.id
                  ? 'border-indigo-500 bg-indigo-50'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
              onClick={() => setActiveStrategy(strategy.id)}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">{strategy.name}</h3>
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    strategy.status === 'Active'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}
                >
                  {strategy.status}
                </span>
              </div>
              <p className="text-sm text-gray-600 mb-4">{strategy.description}</p>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-lg font-semibold text-gray-900">{strategy.performance}</p>
                  <p className="text-xs text-gray-600">Performance</p>
                </div>
                <div>
                  <p className="text-lg font-semibold text-gray-900">{strategy.trades}</p>
                  <p className="text-xs text-gray-600">Trades</p>
                </div>
                <div>
                  <p className="text-lg font-semibold text-gray-900">{strategy.winRate}</p>
                  <p className="text-xs text-gray-600">Win Rate</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Strategy Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.emoji}</span>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      {renderTabContent()}
    </div>
  );
};

export default StrategiesPage;
